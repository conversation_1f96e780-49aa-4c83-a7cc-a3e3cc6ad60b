#!/usr/bin/env node

/**
 * Script de test pour vérifier la configuration Nodemailer
 * Usage: node scripts/test-nodemailer.mjs
 */

import { config } from 'dotenv';
import nodemailer from 'nodemailer';

// Charger les variables d'environnement
config();

/**
 * Créer un transporteur selon la configuration
 */
function createTransporter() {
  const provider = process.env.EMAIL_PROVIDER || 'smtp';
  
  console.log(`🔧 Configuration du transporteur ${provider}...`);
  
  try {
    switch (provider) {
      case 'gmail':
        return nodemailer.createTransport({
          service: 'gmail',
          auth: {
            type: 'OAuth2',
            user: process.env.EMAIL_FROM,
            clientId: process.env.EMAIL_CLIENT_ID,
            clientSecret: process.env.EMAIL_CLIENT_SECRET,
            refreshToken: process.env.EMAIL_REFRESH_TOKEN,
            accessToken: process.env.EMAIL_ACCESS_TOKEN
          }
        });

      case 'smtp':
        return nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD
          },
          // Ignorer les certificats auto-signés en développement
          tls: {
            rejectUnauthorized: process.env.NODE_ENV === 'production'
          }
        });

      case 'sendgrid':
        return nodemailer.createTransport({
          service: 'SendGrid',
          auth: {
            user: 'apikey',
            pass: process.env.SENDGRID_API_KEY
          }
        });

      default:
        throw new Error(`Fournisseur non supporté: ${provider}`);
    }
  } catch (error) {
    console.error('❌ Erreur lors de la création du transporteur:', error.message);
    return null;
  }
}

/**
 * Tester la connexion
 */
async function testConnection(transporter) {
  console.log('🔍 Test de la connexion...');
  
  try {
    await transporter.verify();
    console.log('✅ Connexion réussie !');
    return true;
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    return false;
  }
}

/**
 * Envoyer un email de test
 */
async function sendTestEmail(transporter) {
  const testEmail = process.env.TEST_EMAIL || process.env.EMAIL_FROM;
  
  console.log(`📧 Envoi d'un email de test à ${testEmail}...`);
  
  const mailOptions = {
    from: {
      name: process.env.EMAIL_FROM_NAME || 'BROpoints Test',
      address: process.env.EMAIL_FROM
    },
    to: testEmail,
    subject: '🧪 Test Nodemailer - BROpoints',
    text: 'Ceci est un email de test envoyé via Nodemailer pour vérifier la configuration.',
    html: `
      <h1>🧪 Test Nodemailer</h1>
      <p>Ceci est un email de test envoyé via <strong>Nodemailer</strong> pour vérifier la configuration.</p>
      <p><strong>Fournisseur:</strong> ${process.env.EMAIL_PROVIDER || 'smtp'}</p>
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
      <hr>
      <p><em>BROpoints - Custom Loyalty Rewards App</em></p>
    `
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email envoyé avec succès !');
    console.log(`📬 Message ID: ${result.messageId}`);
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi:', error.message);
    return false;
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Test de configuration Nodemailer\n');
  
  // Vérifier les variables d'environnement
  const provider = process.env.EMAIL_PROVIDER || 'smtp';
  console.log(`📋 Fournisseur: ${provider}`);
  console.log(`📤 Email expéditeur: ${process.env.EMAIL_FROM || 'Non défini'}`);
  console.log(`👤 Nom expéditeur: ${process.env.EMAIL_FROM_NAME || 'Non défini'}`);
  console.log(`🎯 Email de test: ${process.env.TEST_EMAIL || process.env.EMAIL_FROM || 'Non défini'}\n`);
  
  // Créer le transporteur
  const transporter = createTransporter();
  if (!transporter) {
    console.log('❌ Impossible de créer le transporteur. Vérifiez votre configuration.');
    process.exit(1);
  }
  
  // Tester la connexion
  const connectionOk = await testConnection(transporter);
  if (!connectionOk) {
    console.log('❌ Test de connexion échoué. Vérifiez vos paramètres.');
    process.exit(1);
  }
  
  // Envoyer un email de test
  const emailOk = await sendTestEmail(transporter);
  if (!emailOk) {
    console.log('❌ Envoi d\'email échoué.');
    process.exit(1);
  }
  
  console.log('\n🎉 Tous les tests sont passés ! Votre configuration Nodemailer fonctionne correctement.');
}

// Exécuter le script
main().catch(error => {
  console.error('💥 Erreur fatale:', error);
  process.exit(1);
});
