<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test d'Intégration - Custom Loyalty Rewards App</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #2E7D32;
        }
        
        .test-button {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 8px;
            transition: transform 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.secondary {
            background: transparent;
            border: 1px solid #2E7D32;
            color: #2E7D32;
        }
        
        .test-button.secondary:hover {
            background: #2E7D32;
            color: white;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: 600;
            margin: 8px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-block {
            background: #f1f3f4;
            padding: 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 12px 0;
        }
        
        .menu-example {
            background: #2E7D32;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 16px 0;
        }
        
        .menu-example a {
            color: white;
            text-decoration: none;
            margin: 0 16px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }
        
        .menu-example a:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .loyalty-points-badge {
            background: #4CAF50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test d'Intégration - Custom Loyalty Rewards App</h1>
    <p>Cette page permet de tester l'API d'ouverture directe du widget de fidélité.</p>
    
    <div class="test-section">
        <h2>📊 État de l'API</h2>
        <div id="api-status" class="status info">Vérification en cours...</div>
        <div id="customer-info"></div>
    </div>
    
    <div class="test-section">
        <h2>🎮 Tests des Méthodes API</h2>
        <p>Testez les différentes méthodes de l'API JavaScript :</p>
        
        <button class="test-button" onclick="testOpenWidget()">
            🔓 Ouvrir le Widget
        </button>
        
        <button class="test-button" onclick="testCloseWidget()">
            🔒 Fermer le Widget
        </button>
        
        <button class="test-button" onclick="testToggleWidget()">
            🔄 Basculer le Widget
        </button>
        
        <button class="test-button secondary" onclick="testHideButton()">
            👁️‍🗨️ Masquer le Bouton Flottant
        </button>
        
        <button class="test-button secondary" onclick="testShowButton()">
            👁️ Afficher le Bouton Flottant
        </button>
        
        <div class="code-block">
            <strong>Code utilisé :</strong><br>
            window.loyaltyWidget.open();<br>
            window.loyaltyWidget.close();<br>
            window.loyaltyWidget.toggle();<br>
            window.loyaltyWidget.hideFloatingButton();<br>
            window.loyaltyWidget.showFloatingButton();
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧭 Exemple d'Intégration Menu</h2>
        <p>Voici comment intégrer le lien dans un menu de navigation :</p>
        
        <div class="menu-example">
            <a href="/">Accueil</a>
            <a href="/collections">Boutique</a>
            <a href="#" onclick="window.loyaltyWidget.open(); return false;">
                Club BROpoints
                <span class="loyalty-points-badge" id="menu-points">0 pts</span>
            </a>
            <a href="/pages/contact">Contact</a>
        </div>
        
        <div class="code-block">
&lt;a href="#" onclick="window.loyaltyWidget.open(); return false;"&gt;<br>
&nbsp;&nbsp;Club BROpoints<br>
&nbsp;&nbsp;&lt;span class="loyalty-points-badge"&gt;0 pts&lt;/span&gt;<br>
&lt;/a&gt;
        </div>
    </div>
    
    <div class="test-section">
        <h2>📱 Test Responsive</h2>
        <p>Testez l'ouverture du widget sur différentes tailles d'écran :</p>
        
        <button class="test-button" onclick="testMobileView()">
            📱 Simuler Vue Mobile
        </button>
        
        <button class="test-button" onclick="testDesktopView()">
            🖥️ Simuler Vue Desktop
        </button>
    </div>
    
    <div class="test-section">
        <h2>🔍 Informations de Debug</h2>
        <div id="debug-info"></div>
        
        <button class="test-button secondary" onclick="refreshDebugInfo()">
            🔄 Actualiser les Infos
        </button>
    </div>

    <script>
        // Variables globales pour les tests
        let originalViewport = null;
        
        // Vérifier l'état de l'API au chargement
        function checkAPIStatus() {
            const statusElement = document.getElementById('api-status');
            const customerInfoElement = document.getElementById('customer-info');
            
            if (typeof window.loyaltyWidget !== 'undefined') {
                statusElement.className = 'status success';
                statusElement.textContent = '✅ API disponible - Widget chargé avec succès';
                
                // Afficher les informations client si disponibles
                if (window.loyaltyWidget.customerData) {
                    const customerData = window.loyaltyWidget.customerData;
                    customerInfoElement.innerHTML = `
                        <div class="code-block">
                            <strong>Données Client :</strong><br>
                            Type: ${customerData.type}<br>
                            Points: ${customerData.points || 0}<br>
                            Niveau: ${customerData.vipLevel || 'Aucun'}<br>
                            Email: ${customerData.email || 'Non disponible'}
                        </div>
                    `;
                    
                    // Mettre à jour l'affichage des points dans le menu
                    const menuPoints = document.getElementById('menu-points');
                    if (menuPoints) {
                        menuPoints.textContent = `${customerData.points || 0} pts`;
                    }
                }
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ API non disponible - Widget non chargé';
            }
        }
        
        // Tests des méthodes API
        function testOpenWidget() {
            if (window.loyaltyWidget) {
                window.loyaltyWidget.open();
                console.log('Widget ouvert');
            } else {
                alert('Widget non disponible');
            }
        }
        
        function testCloseWidget() {
            if (window.loyaltyWidget) {
                window.loyaltyWidget.close();
                console.log('Widget fermé');
            } else {
                alert('Widget non disponible');
            }
        }
        
        function testToggleWidget() {
            if (window.loyaltyWidget) {
                window.loyaltyWidget.toggle();
                console.log('Widget basculé');
            } else {
                alert('Widget non disponible');
            }
        }
        
        function testHideButton() {
            if (window.loyaltyWidget && window.loyaltyWidget.hideFloatingButton) {
                window.loyaltyWidget.hideFloatingButton();
                console.log('Bouton flottant masqué');
            } else {
                alert('Méthode non disponible');
            }
        }
        
        function testShowButton() {
            if (window.loyaltyWidget && window.loyaltyWidget.showFloatingButton) {
                window.loyaltyWidget.showFloatingButton();
                console.log('Bouton flottant affiché');
            } else {
                alert('Méthode non disponible');
            }
        }
        
        // Tests responsive
        function testMobileView() {
            if (!originalViewport) {
                originalViewport = document.querySelector('meta[name="viewport"]').content;
            }
            document.querySelector('meta[name="viewport"]').content = 'width=375, initial-scale=1';
            window.dispatchEvent(new Event('resize'));
            console.log('Vue mobile simulée');
        }
        
        function testDesktopView() {
            if (originalViewport) {
                document.querySelector('meta[name="viewport"]').content = originalViewport;
            }
            window.dispatchEvent(new Event('resize'));
            console.log('Vue desktop restaurée');
        }
        
        // Informations de debug
        function refreshDebugInfo() {
            const debugElement = document.getElementById('debug-info');
            
            const debugInfo = {
                'Widget disponible': !!window.loyaltyWidget,
                'Type de widget': typeof window.loyaltyWidget,
                'Méthodes disponibles': window.loyaltyWidget ? Object.getOwnPropertyNames(Object.getPrototypeOf(window.loyaltyWidget)) : 'N/A',
                'État ouvert': window.loyaltyWidget ? window.loyaltyWidget.isOpen : 'N/A',
                'Configuration': window.loyaltyWidgetConfig || 'Non disponible',
                'URL de base API': window.loyaltyWidget ? window.loyaltyWidget.apiBaseUrl : 'N/A'
            };
            
            let debugHTML = '<div class="code-block"><strong>Informations de Debug :</strong><br>';
            for (const [key, value] of Object.entries(debugInfo)) {
                debugHTML += `${key}: ${typeof value === 'object' ? JSON.stringify(value, null, 2) : value}<br>`;
            }
            debugHTML += '</div>';
            
            debugElement.innerHTML = debugHTML;
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Attendre que le widget soit chargé
            setTimeout(checkAPIStatus, 2000);
            setTimeout(refreshDebugInfo, 2000);
        });
        
        // Écouter les événements du widget
        window.addEventListener('loyaltyPointsUpdated', function(event) {
            console.log('Points mis à jour:', event.detail);
            checkAPIStatus();
        });
    </script>
</body>
</html>
