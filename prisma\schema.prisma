// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

// Table de session pour l'authentification Shopify
model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

// Table des paramètres du programme de fidélité
model Settings {
  id              String   @id @default(uuid())
  shop            String   @unique
  earningRate     Float    @default(1.0)    // Taux de gain : points par euro
  redemptionRate  Float    @default(100.0)  // Taux de conversion : points par euro (ex: 100 points = 1€)
  minimumPoints   Int      @default(100)    // Points minimum pour échanger
  expirationDays  Int      @default(365)    // Nombre de jours avant expiration des points
  referralPoints  Int      @default(100)    // Points pour parrainage
  birthdayPoints  Int      @default(250)    // Points pour anniversaire
  widgetEnabled   Boolean  @default(true)   // État du widget
  primaryColor    String   @default("#000000")
  language        String   @default("fr")
  exchangeableProducts String? // JSON des produits échangeables avec des points

  // Paramètres de style du widget
  widgetSecondaryColor String  @default("#4CAF50")
  widgetTextColor     String  @default("#FFFFFF")
  widgetPosition      String  @default("bottom-right")
  widgetSize          String  @default("medium")
  widgetBorderRadius  String  @default("rounded")
  widgetShadow        Boolean @default(true)
  widgetAnimation     Boolean @default(true)
  showPointsOnButton  Boolean @default(true)

  // Paramètres de contenu
  pointsName          String  @default("Points")
  welcomeMessage      String  @default("Bienvenue dans notre programme de fidélité !")
  shopName            String?
  currency            String  @default("EUR")
  customCSS           String?
  emailNotifications  Boolean @default(true)
  senderEmail         String?

  // Configuration du programme VIP
  vipProgramEnabled   Boolean @default(true)
  vipRetentionPeriod  Int     @default(365)
  vipEvaluationPeriod Int     @default(90)

  // Presets de couleurs personnalisés
  customBrandPresets  String  @default("[]") // JSON array

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations avec les autres tables
  customers    Customer[]
  vipLevels    VIPLevel[]
  campaigns    Campaign[]
  waysToEarn   WayToEarn[]
  waysToRedeem WayToRedeem[]

  // Historique des modifications
  settingsHistory SettingsHistory[]
}

// Historique des modifications des paramètres
model SettingsHistory {
  id          String   @id @default(uuid())
  shop        String
  changes     String   // JSON des changements
  createdAt   DateTime @default(now())
  updatedBy   String?  // ID de l'utilisateur qui a fait les modifications

  // Relations
  settings Settings @relation(fields: [shop], references: [shop])

  @@index([shop])
  @@index([createdAt])
}

// Table des clients avec informations étendues
model Customer {
  id          String   @id @default(uuid())
  customerId  String                // ID client Shopify
  shop        String                // Boutique Shopify
  firstName   String?               // Prénom
  lastName    String?               // Nom
  email       String?               // Email
  type        String   @default("guest") // "member" ou "guest"
  points      Int      @default(0)  // Solde actuel
  vipLevel    String?              // Niveau VIP actuel
  totalSpent  Float    @default(0)  // Total dépensé
  ordersCount Int      @default(0)  // Nombre de commandes
  lastOrderAt DateTime?            // Dernière commande
  joinedAt    DateTime @default(now()) // Date d'inscription
  lastUpdated DateTime @default(now())

  // Relations
  settings   Settings       @relation(fields: [shop], references: [shop])
  history    PointsHistory[]
  referrals  Referral[]    @relation("Referrer")
  referredBy Referral[]    @relation("Referred")
  orders     Order[]
  rewards    Reward[]

  @@unique([customerId, shop])
  @@index([shop])
  @@index([type])
  @@index([email])
  @@map("customers")
}

// Historique des transactions
model PointsHistory {
  id          String   @id @default(uuid())
  ledgerId    String
  action      String   // earn, redeem, referral, campaign, etc.
  points      Int
  description String?
  metadata    String?  // JSON en string pour SQLite
  timestamp   DateTime @default(now())
  expiresAt   DateTime?

  // Relations
  customer Customer @relation(fields: [ledgerId], references: [id])

  @@index([ledgerId])
  @@index([timestamp])
}

// Programme de parrainage
model Referral {
  id                String    @id @default(uuid())
  shop              String
  referrerId        String    // ID du parrain
  referredId        String?   // ID du filleul (null pour les templates)
  code              String    // Code de parrainage (non unique pour permettre les multiples)
  status            String    @default("pending") // "active" pour templates, "pending"/"completed" pour instances
  createdAt         DateTime  @default(now())
  completedAt       DateTime?
  expiresAt         DateTime
  parentReferralId  String?   // ID du parrainage parent (null pour les templates)

  // Relations
  referrer Customer  @relation("Referrer", fields: [referrerId], references: [id])
  referred Customer? @relation("Referred", fields: [referredId], references: [id])

  // Relations parent-enfant pour les parrainages multiples
  parent   Referral?  @relation("ReferralHierarchy", fields: [parentReferralId], references: [id])
  children Referral[] @relation("ReferralHierarchy")

  @@index([shop])
  @@index([referrerId])
  @@index([code])
  @@index([parentReferralId])
  @@unique([code, shop]) // Un code unique par shop pour les templates
}

// Configuration du programme VIP
model VIPLevel {
  id              String   @id @default(uuid())
  shop            String
  name            String
  threshold       Float    // Seuil de dépenses
  pointsMultiplier Float   @default(1)
  benefits        String   // JSON en string pour SQLite
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  settings Settings @relation(fields: [shop], references: [shop])

  @@unique([shop, name])
  @@index([shop])
}

// Campagnes de points bonus
model Campaign {
  id        String   @id @default(uuid())
  shop      String
  name      String
  type      String   // multiplier, fixed, percentage
  value     Float
  target    String   // all, collection, products
  targetIds String?  // JSON en string pour SQLite
  startDate DateTime
  endDate   DateTime
  status    String   @default("draft")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  settings Settings @relation(fields: [shop], references: [shop])

  @@index([shop])
  @@index([status])
}

// Configuration du programme de parrainage
model ReferralSettings {
  id              String   @id @default(uuid())
  shop            String   @unique
  active          Boolean  @default(false)
  referrerReward  String   // JSON en string pour SQLite
  referredReward  String   // JSON en string pour SQLite
  minimumPurchase Float    @default(0)
  expiryDays      Int      @default(30)
  customMessage   String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model ProgramSettings {
  id          String   @id @default(uuid())
  status      Boolean  @default(false)
  name        String   @default("Programme de fidélité")
  description String   @default("")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  @@map("program_settings")
}

// Façons de gagner des points
model WayToEarn {
  id           String   @id @default(uuid())
  shop         String
  name         String   // ex: "Place an order"
  description  String   // ex: "5 Points for every €1 spent"
  actionType   String   @default("order") // "order", "signup", "birthday", "referral", etc.
  earningType  String   @default("increments") // "increments" ou "fixed"
  earningValue Float    // Valeur selon le type
  icon         String   @default("order") // Nom de l'icône
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  settings Settings @relation(fields: [shop], references: [shop])

  @@index([shop])
  @@index([isActive])
  @@index([actionType])
}

// Façons d'échanger des points
model WayToRedeem {
  id           String   @id @default(uuid())
  shop         String
  name         String   // ex: "Order discount"
  description  String   // ex: "Get discount on your orders"
  redeemType   String   @default("discount") // "discount", "product", "shipping", "coupon"
  redeemValue  Float    // Valeur de l'échange
  pointsCost   Int      // Coût en points (peut être 0 pour les coupons configurables)
  icon         String   @default("discount") // Nom de l'icône
  isActive     Boolean  @default(true)

  // Nouveaux champs pour les limites et configuration
  isConfigurable Boolean @default(false) // Si true, le client peut choisir le montant
  minPoints     Int?     // Points minimum pour ce type de redemption
  maxPoints     Int?     // Points maximum pour ce type de redemption
  minValue      Float?   // Valeur minimum (pour les coupons configurables)
  maxValue      Float?   // Valeur maximum (pour les coupons configurables)
  expiryDays    Int      @default(30) // Nombre de jours avant expiration
  usageLimit    Int      @default(1)  // Nombre d'utilisations par coupon

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  settings Settings @relation(fields: [shop], references: [shop])

  @@index([shop])
  @@index([isActive])
  @@index([redeemType])
}

// Commandes des clients
model Order {
  id            String   @id @default(uuid())
  orderId       String   // ID de la commande Shopify
  customerDbId  String   // ID du client dans notre DB
  shopifyCustomerId String // ID du client Shopify
  shop          String   // Boutique
  total         Float    // Montant total
  status        String   // Statut de la commande
  paymentStatus String   // Statut du paiement
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  customer Customer @relation(fields: [customerDbId], references: [id])

  @@unique([orderId, shop])
  @@index([shop])
  @@index([customerDbId])
  @@index([shopifyCustomerId])
  @@index([createdAt])
}

// Récompenses échangées par les clients
model Reward {
  id          String   @id @default(uuid())
  customerId  String   // ID du client dans notre DB
  shopifyCustomerId String // ID du client Shopify
  shop        String   // Boutique
  type        String   // Type de récompense (discount, product, shipping, coupon)
  name        String   // Nom de la récompense
  value       Float    // Valeur de la récompense
  pointsCost  Int      // Coût en points
  code        String?  // Code de réduction si applicable
  discountId  String?  // ID Shopify du discount
  status      String   @default("active") // active, used, expired
  expiresAt   DateTime?
  usedAt      DateTime?
  orderId     String?  // ID de la commande où le code a été utilisé
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  customer Customer @relation(fields: [customerId], references: [id])

  @@index([shop])
  @@index([customerId])
  @@index([shopifyCustomerId])
  @@index([status])
  @@index([code])
  @@index([createdAt])
}
