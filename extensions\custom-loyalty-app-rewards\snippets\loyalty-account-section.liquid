{% comment %}
  Snippet : Section fidélité pour l'espace client
  Usage : {% render 'loyalty-account-section' %}
  
  Paramètres optionnels :
  - title: Titre de la section (défaut: "Programme de Fidélité")
  - show_summary: Affiche<PERSON> le résumé (défaut: true)
  - show_activity: Afficher le lien vers l'activité (défaut: true)
{% endcomment %}

{% assign section_title = title | default: "Programme de Fidélité" %}
{% assign show_summary = show_summary | default: true %}
{% assign show_activity = show_activity | default: true %}

<div class="loyalty-account-section">
  <div class="loyalty-account-header">
    <h3 class="loyalty-account-title">{{ section_title }}</h3>
    {% if customer %}
      <span class="loyalty-status-badge" id="loyalty-status-badge">Membre</span>
    {% endif %}
  </div>
  
  {% if customer %}
    <!-- Client connecté -->
    {% if show_summary %}
      <div class="loyalty-account-summary">
        <div class="loyalty-stats-grid">
          <div class="loyalty-stat-card">
            <div class="loyalty-stat-icon">⭐</div>
            <div class="loyalty-stat-content">
              <span class="loyalty-stat-value" id="account-points-display">
                <span class="loyalty-loading">Chargement...</span>
              </span>
              <span class="loyalty-stat-label">Points disponibles</span>
            </div>
          </div>
          
          <div class="loyalty-stat-card">
            <div class="loyalty-stat-icon">🛍️</div>
            <div class="loyalty-stat-content">
              <span class="loyalty-stat-value" id="account-orders-display">
                {{ customer.orders_count | default: 0 }}
              </span>
              <span class="loyalty-stat-label">Commandes</span>
            </div>
          </div>
          
          <div class="loyalty-stat-card">
            <div class="loyalty-stat-icon">💰</div>
            <div class="loyalty-stat-content">
              <span class="loyalty-stat-value" id="account-spent-display">
                {{ customer.total_spent | money }}
              </span>
              <span class="loyalty-stat-label">Total dépensé</span>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    
    <div class="loyalty-account-actions">
      <button type="button" 
              onclick="window.loyaltyWidget.open()" 
              class="loyalty-btn loyalty-btn-primary">
        🏆 Voir mes récompenses
      </button>
      
      {% if show_activity %}
        <button type="button" 
                onclick="window.loyaltyWidget.open(); setTimeout(() => window.loyaltyWidget.showYourActivitySection(), 500);" 
                class="loyalty-btn loyalty-btn-secondary">
          📋 Mon activité
        </button>
      {% endif %}
    </div>
    
    <div class="loyalty-quick-info">
      <div class="loyalty-info-item">
        <span class="loyalty-info-icon">🎯</span>
        <span class="loyalty-info-text">
          Gagnez des points à chaque achat et débloquez des récompenses exclusives
        </span>
      </div>
      
      <div class="loyalty-info-item">
        <span class="loyalty-info-icon">👥</span>
        <span class="loyalty-info-text">
          Parrainez vos amis et gagnez encore plus de points
        </span>
      </div>
    </div>
    
  {% else %}
    <!-- Client non connecté -->
    <div class="loyalty-guest-prompt">
      <div class="loyalty-guest-icon">🎁</div>
      <h4>Rejoignez notre programme de fidélité !</h4>
      <p>Gagnez des points à chaque achat et débloquez des récompenses exclusives.</p>
      
      <div class="loyalty-guest-benefits">
        <div class="loyalty-benefit-item">
          <span class="loyalty-benefit-icon">⭐</span>
          <span>Points à chaque achat</span>
        </div>
        <div class="loyalty-benefit-item">
          <span class="loyalty-benefit-icon">🎁</span>
          <span>Récompenses exclusives</span>
        </div>
        <div class="loyalty-benefit-item">
          <span class="loyalty-benefit-icon">👥</span>
          <span>Bonus de parrainage</span>
        </div>
      </div>
      
      <a href="{{ routes.account_login_url }}" class="loyalty-btn loyalty-btn-primary">
        Se connecter pour rejoindre
      </a>
    </div>
  {% endif %}
</div>

<style>
  .loyalty-account-section {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
  }
  
  .loyalty-account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .loyalty-account-title {
    margin: 0;
    color: #2E7D32;
    font-size: 24px;
    font-weight: 700;
  }
  
  .loyalty-status-badge {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
  }
  
  .loyalty-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .loyalty-stat-card {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .loyalty-stat-icon {
    font-size: 24px;
  }
  
  .loyalty-stat-content {
    display: flex;
    flex-direction: column;
  }
  
  .loyalty-stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #2E7D32;
  }
  
  .loyalty-stat-label {
    font-size: 12px;
    color: #757575;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .loyalty-loading {
    opacity: 0.7;
    font-size: 14px;
  }
  
  .loyalty-account-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .loyalty-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .loyalty-btn-primary {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
  }
  
  .loyalty-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
  }
  
  .loyalty-btn-secondary {
    background: transparent;
    border: 1px solid #2E7D32;
    color: #2E7D32;
  }
  
  .loyalty-btn-secondary:hover {
    background: #2E7D32;
    color: white;
  }
  
  .loyalty-quick-info {
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
  }
  
  .loyalty-info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .loyalty-info-icon {
    font-size: 18px;
  }
  
  .loyalty-info-text {
    color: #757575;
    font-size: 14px;
  }
  
  .loyalty-guest-prompt {
    text-align: center;
    padding: 20px;
  }
  
  .loyalty-guest-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .loyalty-guest-benefits {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin: 20px 0;
  }
  
  .loyalty-benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .loyalty-benefit-icon {
    font-size: 24px;
  }
  
  @media (max-width: 768px) {
    .loyalty-account-section {
      padding: 16px;
    }
    
    .loyalty-account-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }
    
    .loyalty-stats-grid {
      grid-template-columns: 1fr;
    }
    
    .loyalty-account-actions {
      flex-direction: column;
    }
    
    .loyalty-btn {
      width: 100%;
    }
    
    .loyalty-guest-benefits {
      flex-direction: column;
      gap: 16px;
    }
  }
</style>

<script>
  // Mettre à jour les informations du compte
  function updateAccountLoyaltyInfo() {
    if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
      const customerData = window.loyaltyWidget.customerData;
      
      // Mettre à jour les points
      const pointsElement = document.getElementById('account-points-display');
      if (pointsElement) {
        pointsElement.textContent = `${customerData.points || 0}`;
      }
      
      // Mettre à jour le statut
      const statusElement = document.getElementById('loyalty-status-badge');
      if (statusElement && customerData.vipLevel) {
        statusElement.textContent = customerData.vipLevel;
      }
    }
  }
  
  // Initialiser l'affichage
  document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateAccountLoyaltyInfo, 2000);
  });
  
  // Écouter les mises à jour
  window.addEventListener('loyaltyPointsUpdated', updateAccountLoyaltyInfo);
</script>
