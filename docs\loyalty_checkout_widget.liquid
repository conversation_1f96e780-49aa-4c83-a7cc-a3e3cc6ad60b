{% comment %}
  Bloc Liquid : Widget de fidélité pour page checkout
  Affiche les points à gagner avec la commande actuelle
{% endcomment %}

{% assign cart_total = cart.total_price %}
{% assign estimated_points = cart_total | divided_by: 100 | round %}

<div class="loyalty-checkout-widget" 
     onclick="window.loyaltyWidget && window.loyaltyWidget.open()">
  
  {% if customer %}
    <!-- Client connecté -->
    <div class="loyalty-checkout-content">
      <div class="loyalty-checkout-header">
        <div class="loyalty-checkout-icon">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="loyalty-checkout-title">
          <h3>Vous gagnez des BROpoints !</h3>
          <p>Avec cette commande</p>
        </div>
      </div>
      
      <div class="loyalty-checkout-details">
        <div class="loyalty-points-breakdown">
          <div class="points-line">
            <span class="points-label">Points de base :</span>
            <span class="points-value" data-base-points="{{ estimated_points }}">+{{ estimated_points }}</span>
          </div>
          
          {% if customer.tags contains 'vip' %}
            {% assign vip_multiplier = customer.metafields.loyalty.vip_multiplier | default: 1.25 %}
            {% assign bonus_points = estimated_points | times: vip_multiplier | minus: estimated_points | round %}
            <div class="points-line vip-bonus">
              <span class="points-label">
                Bonus {{ customer.metafields.loyalty.vip_level | default: 'VIP' }} :
              </span>
              <span class="points-value bonus">+{{ bonus_points }}</span>
            </div>
          {% endif %}
          
          <div class="points-line total">
            <span class="points-label">Total :</span>
            <span class="points-value total-points" data-total-points="{{ estimated_points }}">
              +{{ estimated_points }} points
            </span>
          </div>
        </div>
        
        <div class="loyalty-checkout-balance">
          <div class="current-balance">
            <span class="balance-label">Solde actuel :</span>
            <span class="balance-value" id="checkout-current-balance">-- points</span>
          </div>
          <div class="new-balance">
            <span class="balance-label">Nouveau solde :</span>
            <span class="balance-value" id="checkout-new-balance">-- points</span>
          </div>
        </div>
      </div>
      
      <div class="loyalty-checkout-cta">
        <span class="cta-text">Voir mes récompenses disponibles</span>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    
  {% else %}
    <!-- Client non connecté -->
    <div class="loyalty-checkout-content guest">
      <div class="loyalty-checkout-header">
        <div class="loyalty-checkout-icon">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor"/>
          </svg>
        </div>
        <div class="loyalty-checkout-title">
          <h3>Manquez pas {{ estimated_points }} points !</h3>
          <p>Rejoignez BROpoints gratuitement</p>
        </div>
      </div>
      
      <div class="loyalty-checkout-benefits">
        <div class="benefit-item">
          <span class="benefit-icon">🎁</span>
          <span>{{ estimated_points }} points avec cette commande</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-icon">⭐</span>
          <span>Points à chaque achat futur</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-icon">🎯</span>
          <span>Récompenses exclusives</span>
        </div>
      </div>
      
      <div class="loyalty-checkout-cta">
        <span class="cta-text">Rejoindre le programme maintenant</span>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
  {% endif %}
</div>

<style>
  .loyalty-checkout-widget {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
    border-radius: 16px;
    padding: 20px;
    margin: 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .loyalty-checkout-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .loyalty-checkout-widget:hover::before {
    opacity: 1;
  }
  
  .loyalty-checkout-widget:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(46, 125, 50, 0.3);
  }
  
  .loyalty-checkout-content {
    position: relative;
    z-index: 1;
  }
  
  .loyalty-checkout-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .loyalty-checkout-icon {
    color: #FFD700;
    flex-shrink: 0;
  }
  
  .loyalty-checkout-title h3 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
  }
  
  .loyalty-checkout-title p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
  }
  
  .loyalty-checkout-details {
    margin-bottom: 16px;
  }
  
  .loyalty-points-breakdown {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .points-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .points-line:last-child {
    margin-bottom: 0;
  }
  
  .points-line.total {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 8px;
    margin-top: 8px;
    font-weight: 700;
  }
  
  .points-line.vip-bonus .points-value {
    color: #FFD700;
  }
  
  .points-label {
    font-size: 14px;
  }
  
  .points-value {
    font-weight: 600;
    font-size: 16px;
  }
  
  .points-value.bonus {
    color: #FFD700;
  }
  
  .loyalty-checkout-balance {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px;
  }
  
  .current-balance,
  .new-balance {
    text-align: center;
  }
  
  .balance-label {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 4px;
  }
  
  .balance-value {
    display: block;
    font-weight: 700;
    font-size: 16px;
  }
  
  .loyalty-checkout-benefits {
    margin-bottom: 16px;
  }
  
  .benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 14px;
  }
  
  .benefit-item:last-child {
    margin-bottom: 0;
  }
  
  .benefit-icon {
    font-size: 18px;
    flex-shrink: 0;
  }
  
  .loyalty-checkout-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .loyalty-checkout-widget:hover .loyalty-checkout-cta {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
  }
  
  .cta-text {
    font-size: 14px;
  }
  
  /* Version invité */
  .loyalty-checkout-content.guest .loyalty-checkout-title h3 {
    font-size: 18px;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .loyalty-checkout-widget {
      padding: 16px;
      margin: 16px 0;
    }
    
    .loyalty-checkout-header {
      gap: 12px;
      margin-bottom: 12px;
    }
    
    .loyalty-checkout-title h3 {
      font-size: 18px;
    }
    
    .loyalty-checkout-balance {
      grid-template-columns: 1fr;
      gap: 8px;
    }
    
    .points-value {
      font-size: 14px;
    }
  }
  
  /* Animation au chargement */
  @keyframes loyaltySlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .loyalty-checkout-widget {
    animation: loyaltySlideIn 0.6s ease-out;
  }
</style>

<script>
  // Mettre à jour les informations de fidélité au checkout
  document.addEventListener('DOMContentLoaded', function() {
    const checkoutWidget = document.querySelector('.loyalty-checkout-widget');
    if (!checkoutWidget) return;
    
    // Mettre à jour le solde actuel et nouveau solde
    function updateCheckoutBalance() {
      if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
        const currentPoints = window.loyaltyWidget.customerData.points || 0;
        const earnedPoints = parseInt(checkoutWidget.querySelector('[data-total-points]')?.getAttribute('data-total-points') || '0');
        const newPoints = currentPoints + earnedPoints;
        
        const currentBalanceElement = document.getElementById('checkout-current-balance');
        const newBalanceElement = document.getElementById('checkout-new-balance');
        
        if (currentBalanceElement) {
          currentBalanceElement.textContent = `${currentPoints} points`;
        }
        
        if (newBalanceElement) {
          newBalanceElement.textContent = `${newPoints} points`;
        }
      }
    }
    
    // Mettre à jour les points en fonction du total du panier
    function updateCartPoints() {
      // Écouter les changements du panier (si le thème le supporte)
      if (window.cart && window.cart.total_price) {
        const cartTotal = window.cart.total_price;
        const basePoints = Math.round(cartTotal / 100);
        
        const basePointsElement = checkoutWidget.querySelector('[data-base-points]');
        const totalPointsElement = checkoutWidget.querySelector('[data-total-points]');
        
        if (basePointsElement) {
          basePointsElement.textContent = `+${basePoints}`;
          basePointsElement.setAttribute('data-base-points', basePoints);
        }
        
        if (totalPointsElement) {
          totalPointsElement.textContent = `+${basePoints} points`;
          totalPointsElement.setAttribute('data-total-points', basePoints);
        }
        
        updateCheckoutBalance();
      }
    }
    
    // Initialiser
    setTimeout(() => {
      updateCheckoutBalance();
      updateCartPoints();
    }, 1000);
    
    // Écouter les mises à jour du widget de fidélité
    window.addEventListener('loyaltyPointsUpdated', updateCheckoutBalance);
    
    // Écouter les changements du panier (si disponible)
    if (window.addEventListener) {
      window.addEventListener('cart:updated', updateCartPoints);
      window.addEventListener('cart:changed', updateCartPoints);
    }
  });
</script>

{% schema %}
{
  "name": "Widget Fidélité Checkout",
  "target": "section",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "checkbox",
      "id": "show_for_guests",
      "label": "Afficher pour les visiteurs non connectés",
      "default": true,
      "info": "Encourage l'inscription au programme"
    },
    {
      "type": "checkbox",
      "id": "show_balance_preview",
      "label": "Afficher l'aperçu du nouveau solde",
      "default": true,
      "info": "Montre le solde après ajout des points"
    },
    {
      "type": "text",
      "id": "custom_title_member",
      "label": "Titre pour les membres",
      "placeholder": "Vous gagnez des BROpoints !",
      "info": "Titre affiché aux clients connectés"
    },
    {
      "type": "text",
      "id": "custom_title_guest",
      "label": "Titre pour les visiteurs",
      "placeholder": "Manquez pas {points} points !",
      "info": "Utilisez {points} pour afficher le nombre de points"
    }
  ]
}
{% endschema %}
