{% comment %}
  Snippet : Affichage des points à gagner sur une page produit
  Usage : {% render 'loyalty-product-points', product: product %}
  
  Paramètres :
  - product: Objet produit Shopify (requis)
  - variant: Variante spécifique (optionnel)
  - show_member_only: <PERSON><PERSON><PERSON><PERSON> le texte "membre uniquement" (défaut: false)
{% endcomment %}

{% assign current_variant = variant | default: product.selected_or_first_available_variant %}
{% assign product_price = current_variant.price | default: product.price %}
{% assign show_member_text = show_member_only | default: false %}

<!-- Calcul approximatif des points (1 point par euro par défaut) -->
{% assign estimated_points = product_price | divided_by: 100 | round %}

<div class="loyalty-product-points" data-product-id="{{ product.id }}">
  {% if customer %}
    <!-- Client connecté -->
    <div class="loyalty-earn-info">
      <span class="loyalty-icon">⭐</span>
      <span class="loyalty-earn-text">
        Gagnez <strong class="loyalty-points-amount" data-points="{{ estimated_points }}">{{ estimated_points }}</strong> points avec cet achat
      </span>
    </div>
    
    {% if show_member_text %}
      <div class="loyalty-member-info">
        <span class="loyalty-member-badge">Membre</span>
        <span>Profitez de vos avantages fidélité</span>
      </div>
    {% endif %}
  {% else %}
    <!-- Client non connecté -->
    <div class="loyalty-guest-info">
      <span class="loyalty-icon">⭐</span>
      <span class="loyalty-guest-text">
        <a href="{{ routes.account_login_url }}" class="loyalty-login-link">Connectez-vous</a> 
        pour gagner {{ estimated_points }} points avec cet achat
      </span>
    </div>
  {% endif %}
  
  <button type="button" 
          onclick="window.loyaltyWidget.open()" 
          class="loyalty-learn-more">
    En savoir plus sur le programme
  </button>
</div>

<style>
  .loyalty-product-points {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
    border-left: 4px solid #2E7D32;
  }
  
  .loyalty-earn-info,
  .loyalty-guest-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .loyalty-icon {
    font-size: 18px;
    color: #2E7D32;
  }
  
  .loyalty-points-amount {
    color: #2E7D32;
    font-weight: 700;
  }
  
  .loyalty-member-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
  }
  
  .loyalty-member-badge {
    background: #2E7D32;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .loyalty-login-link {
    color: #2E7D32;
    font-weight: 600;
    text-decoration: none;
  }
  
  .loyalty-login-link:hover {
    text-decoration: underline;
  }
  
  .loyalty-learn-more {
    background: transparent;
    border: 1px solid #2E7D32;
    color: #2E7D32;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .loyalty-learn-more:hover {
    background: #2E7D32;
    color: white;
  }
  
  @media (max-width: 768px) {
    .loyalty-product-points {
      padding: 12px;
      margin: 12px 0;
    }
    
    .loyalty-earn-info,
    .loyalty-guest-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
    
    .loyalty-learn-more {
      width: 100%;
      padding: 12px;
    }
  }
</style>

<script>
  // Mettre à jour les points en fonction de la variante sélectionnée
  function updateProductPoints() {
    const productPointsContainer = document.querySelector('.loyalty-product-points');
    if (!productPointsContainer) return;
    
    // Écouter les changements de variante
    const variantSelectors = document.querySelectorAll('input[name="id"], select[name="id"]');
    variantSelectors.forEach(selector => {
      selector.addEventListener('change', function() {
        const selectedVariant = this.value;
        // Ici, vous pouvez implémenter la logique pour recalculer les points
        // basée sur le prix de la variante sélectionnée
        
        // Pour l'instant, on utilise une estimation simple
        const priceElement = document.querySelector('.price .money, .price-item .money');
        if (priceElement) {
          const priceText = priceElement.textContent.replace(/[^\d.,]/g, '');
          const price = parseFloat(priceText.replace(',', '.')) * 100; // Convertir en centimes
          const points = Math.round(price / 100); // 1 point par euro
          
          const pointsElement = productPointsContainer.querySelector('.loyalty-points-amount');
          if (pointsElement) {
            pointsElement.textContent = points;
          }
        }
      });
    });
  }
  
  document.addEventListener('DOMContentLoaded', updateProductPoints);
</script>
