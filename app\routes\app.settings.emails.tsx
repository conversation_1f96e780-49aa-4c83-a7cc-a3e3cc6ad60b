import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useActionData, useLoaderData, useSubmit } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  Text,
  BlockStack,
  Banner,
  Checkbox,
  Toast,
  Frame,
  Badge,
  InlineStack,
  Box,
  Divider
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { authenticate } from "../shopify.server";
import { getSiteSettings, upsertSiteSettings } from "../models/SiteSettings.server";
import { isEmailServiceConfigured } from "../services/emailService.server";
import { useTranslation } from "../hooks/useTranslation";

type ActionData =
  | { success: true; message: string }
  | { error: string };

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  const settings = await getSiteSettings(session.shop);
  const emailConfigured = isEmailServiceConfigured();

  // Obtenir les informations de configuration
  const emailProvider = process.env.EMAIL_PROVIDER || 'smtp';
  const configInfo = getEmailConfigurationInfo();

  return json({
    settings: settings || {
      emailNotifications: true,
      shopName: session.shop,
      pointsName: "Points"
    },
    emailConfigured,
    emailProvider,
    configInfo,
    shop: session.shop
  });
};

// Fonction pour obtenir les informations de configuration selon le fournisseur
function getEmailConfigurationInfo() {
  const provider = process.env.EMAIL_PROVIDER || 'smtp';

  switch (provider) {
    case 'gmail':
      return {
        provider: 'Gmail OAuth2',
        requiredVars: [
          { key: 'EMAIL_CLIENT_ID', description: 'Client ID Google' },
          { key: 'EMAIL_CLIENT_SECRET', description: 'Client Secret Google' },
          { key: 'EMAIL_REFRESH_TOKEN', description: 'Refresh Token Google' },
          { key: 'EMAIL_FROM', description: 'Adresse email Gmail' },
          { key: 'EMAIL_ENABLED=true', description: 'Activer le service' }
        ]
      };
    case 'outlook':
      return {
        provider: 'Outlook OAuth2',
        requiredVars: [
          { key: 'EMAIL_CLIENT_ID', description: 'Client ID Microsoft' },
          { key: 'EMAIL_CLIENT_SECRET', description: 'Client Secret Microsoft' },
          { key: 'EMAIL_REFRESH_TOKEN', description: 'Refresh Token Microsoft' },
          { key: 'EMAIL_FROM', description: 'Adresse email Outlook' },
          { key: 'EMAIL_ENABLED=true', description: 'Activer le service' }
        ]
      };
    case 'smtp':
      return {
        provider: 'SMTP Personnalisé',
        requiredVars: [
          { key: 'SMTP_HOST', description: 'Serveur SMTP (ex: smtp.gmail.com)' },
          { key: 'SMTP_USER', description: 'Nom d\'utilisateur SMTP' },
          { key: 'SMTP_PASSWORD', description: 'Mot de passe SMTP' },
          { key: 'EMAIL_FROM', description: 'Adresse email d\'expédition' },
          { key: 'EMAIL_ENABLED=true', description: 'Activer le service' }
        ]
      };
    case 'sendgrid':
      return {
        provider: 'SendGrid',
        requiredVars: [
          { key: 'SENDGRID_API_KEY', description: 'Clé API SendGrid' },
          { key: 'EMAIL_FROM', description: 'Adresse email d\'expédition' },
          { key: 'EMAIL_FROM_NAME', description: 'Nom d\'expédition' },
          { key: 'EMAIL_ENABLED=true', description: 'Activer le service' }
        ]
      };
    default:
      return {
        provider: 'Non configuré',
        requiredVars: [
          { key: 'EMAIL_PROVIDER', description: 'Fournisseur (gmail, outlook, smtp, sendgrid)' }
        ]
      };
  }
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "saveEmailSettings") {
    try {
      const emailSettings = {
        emailNotifications: formData.get("emailNotifications") === "true",
        shopName: formData.get("shopName") as string,
        pointsName: formData.get("pointsName") as string,
      };

      await upsertSiteSettings(session.shop, emailSettings);
      
      return json({ 
        success: true, 
        message: "Paramètres d'emails sauvegardés avec succès" 
      });
    } catch (error) {
      console.error("Error saving email settings:", error);
      return json({ 
        error: "Erreur lors de la sauvegarde des paramètres d'emails" 
      }, { status: 500 });
    }
  }

  if (action === "testEmail") {
    try {
      const { sendEmail, EmailType } = await import("../services/emailService.server");

      // Envoyer un email de test à l'adresse configurée
      const testEmail = process.env.TEST_EMAIL || "<EMAIL>";
      const result = await sendEmail(
        EmailType.WELCOME,
        {
          to: testEmail,
          toName: "Test User",
          templateData: {
            customerName: "Test User",
            welcomePoints: 100,
            shopName: session.shop,
            shopUrl: `https://${session.shop}`,
            pointsName: "BROpoints"
          }
        },
        undefined,
        session.shop
      );

      if (result.success) {
        return json({
          success: true,
          message: `Email de test envoyé avec succès à ${testEmail}`
        });
      } else {
        return json({
          error: `Erreur lors de l'envoi: ${result.error}`
        }, { status: 500 });
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      return json({
        error: "Erreur lors de l'envoi de l'email de test"
      }, { status: 500 });
    }
  }

  return json({ error: "Action non reconnue" }, { status: 400 });
};

export default function EmailSettings() {
  const { settings, emailConfigured, emailProvider, configInfo, shop } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>() as ActionData | undefined;
  const submit = useSubmit();
  const { t } = useTranslation();

  const [emailEnabled, setEmailEnabled] = useState(settings.emailNotifications);
  const [shopName, setShopName] = useState(settings.shopName || shop);
  const [pointsName, setPointsName] = useState(settings.pointsName || "Points");
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  console.log("emailConfigured : ", emailConfigured);
  // Gérer les réponses de l'action
  useEffect(() => {
    if (actionData) {
      if ('success' in actionData) {
        setToastMessage((actionData as any).message);
        setToastActive(true);
      } else if ('error' in actionData) {
        setToastMessage((actionData as any).error);
        setToastActive(true);
      }
    }
  }, [actionData]);

  const handleSave = useCallback(() => {
    const formData = new FormData();
    formData.append("action", "saveEmailSettings");
    formData.append("emailNotifications", emailEnabled.toString());
    formData.append("shopName", shopName);
    formData.append("pointsName", pointsName);
    
    submit(formData, { method: "post" });
  }, [submit, emailEnabled, shopName, pointsName]);

  const handleTestEmail = useCallback(() => {
    const formData = new FormData();
    formData.append("action", "testEmail");
    submit(formData, { method: "post" });
  }, [submit]);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={() => setToastActive(false)}
    />
  ) : null;

  return (
    <Frame>
      <AdminLayout>
        <Page
          title={t("admin.emails.title")}
          subtitle={t("admin.emails.subtitle")}
          backAction={{ content: t("admin.emails.backToSettings"), url: "/app/settings" }}
        >
          <Layout>
            <Layout.Section>
              {/* État de la configuration */}
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">{t("admin.emails.configurationStatus")}</Text>

                  <InlineStack gap="400" align="space-between">
                    <Text as="span">{t("admin.emails.emailService")}</Text>
                    <Badge tone={emailConfigured ? "success" : "critical"}>
                      {emailConfigured ? t("admin.emails.configured") : t("admin.emails.notConfigured")}
                    </Badge>
                  </InlineStack>

                  <InlineStack gap="400" align="space-between">
                    <Text as="span">Fournisseur configuré</Text>
                    <Badge tone="info">
                      {configInfo.provider}
                    </Badge>
                  </InlineStack>

                  <InlineStack gap="400" align="space-between">
                    <Text as="span">{t("admin.emails.notificationsEnabled")}</Text>
                    <Badge tone={emailEnabled ? "success" : "attention"}>
                      {emailEnabled ? t("admin.emails.enabled") : t("admin.emails.disabled")}
                    </Badge>
                  </InlineStack>

                  {!emailConfigured && (
                    <Banner tone="warning">
                      <p>
                        <strong>Configuration requise :</strong> Pour activer les emails avec {configInfo.provider},
                        configurez les variables d'environnement suivantes :
                      </p>
                      <ul style={{ marginTop: "8px", paddingLeft: "20px" }}>
                        {configInfo.requiredVars.map((varInfo, index) => (
                          <li key={index}>
                            <code>{varInfo.key}</code> - {varInfo.description}
                          </li>
                        ))}
                      </ul>
                      <p style={{ marginTop: "12px", fontSize: "14px", color: "#666" }}>
                        💡 Consultez le fichier <code>.env.email.example</code> pour des exemples de configuration.
                      </p>
                    </Banner>
                  )}
                </BlockStack>
              </Card>

              {/* Paramètres généraux */}
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">{t("admin.emails.generalSettings")}</Text>

                  <FormLayout>
                    <Checkbox
                      label={t("admin.emails.enableNotifications")}
                      checked={emailEnabled}
                      onChange={setEmailEnabled}
                      helpText={t("admin.emails.enableNotificationsHelp")}
                    />

                    <TextField
                      label={t("admin.emails.shopName")}
                      value={shopName}
                      onChange={setShopName}
                      helpText={t("admin.emails.shopNameHelp")}
                      autoComplete="off"
                    />

                    <TextField
                      label={t("admin.emails.pointsName")}
                      value={pointsName}
                      onChange={setPointsName}
                      helpText={t("admin.emails.pointsNameHelp")}
                      autoComplete="off"
                    />
                  </FormLayout>
                </BlockStack>
              </Card>

              {/* Types d'emails */}
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">{t("admin.emails.automaticEmailTypes")}</Text>

                  <Box>
                    <BlockStack gap="300">
                      <InlineStack gap="400" align="space-between">
                        <Box>
                          <Text as="span" variant="bodyMd" fontWeight="semibold">{t("admin.emails.welcomeEmail")}</Text>
                          <Text as="span" variant="bodySm" tone="subdued">{t("admin.emails.welcomeEmailDesc")}</Text>
                        </Box>
                        <Badge tone="success">{t("admin.emails.active")}</Badge>
                      </InlineStack>

                      <Divider />

                      <InlineStack gap="400" align="space-between">
                        <Box>
                          <Text as="span" variant="bodyMd" fontWeight="semibold">{t("admin.emails.pointsEarned")}</Text>
                          <Text as="span" variant="bodySm" tone="subdued">{t("admin.emails.pointsEarnedDesc")}</Text>
                        </Box>
                        <Badge tone="success">{t("admin.emails.active")}</Badge>
                      </InlineStack>

                      <Divider />

                      <InlineStack gap="400" align="space-between">
                        <Box>
                          <Text as="span" variant="bodyMd" fontWeight="semibold">{t("admin.emails.pointsUsed")}</Text>
                          <Text as="span" variant="bodySm" tone="subdued">{t("admin.emails.pointsUsedDesc")}</Text>
                        </Box>
                        <Badge tone="success">{t("admin.emails.active")}</Badge>
                      </InlineStack>

                      <Divider />

                      <InlineStack gap="400" align="space-between">
                        <Box>
                          <Text as="span" variant="bodyMd" fontWeight="semibold">{t("admin.emails.levelPromotion")}</Text>
                          <Text as="span" variant="bodySm" tone="subdued">{t("admin.emails.levelPromotionDesc")}</Text>
                        </Box>
                        <Badge tone="success">{t("admin.emails.active")}</Badge>
                      </InlineStack>

                      <Divider />

                      <InlineStack gap="400" align="space-between">
                        <Box>
                          <Text as="span" variant="bodyMd" fontWeight="semibold">{t("admin.emails.referralReward")}</Text>
                          <Text as="span" variant="bodySm" tone="subdued">{t("admin.emails.referralRewardDesc")}</Text>
                        </Box>
                        <Badge tone="success">{t("admin.emails.active")}</Badge>
                      </InlineStack>
                    </BlockStack>
                  </Box>
                </BlockStack>
              </Card>

              {/* Actions */}
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">{t("admin.emails.actions")}</Text>

                  <InlineStack gap="300">
                    <Button
                      variant="primary"
                      onClick={handleSave}
                    >
                      {t("admin.emails.saveSettings")}
                    </Button>

                    <Button
                      onClick={handleTestEmail}
                      disabled={!emailConfigured || !emailEnabled}
                    >
                      {t("admin.emails.sendTestEmail")}
                    </Button>
                  </InlineStack>

                  {(!emailConfigured || !emailEnabled) && (
                    <Text as="span" variant="bodySm" tone="subdued">
                      {t("admin.emails.testEmailRequirement")}
                    </Text>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
        </Page>
      </AdminLayout>
      {toastMarkup}
    </Frame>
  );
}
