/**
 * Service de déclenchement automatique des emails
 * Gère l'envoi d'emails basé sur les événements du programme de fidélité
 */

import { sendEmail, EmailType } from './emailService.server';
import { getSiteSettings } from '../models/SiteSettings.server';
import { getCustomerById } from '../models/Customer.server';

/**
 * Interface pour les événements de déclenchement
 */
export interface TriggerEvent {
  shop: string;
  customerId: string;
  eventType: EmailType;
  eventData: Record<string, any>;
}

/**
 * Envoyer un email de bienvenue à un nouveau membre
 */
export async function triggerWelcomeEmail(
  shop: string,
  customerId: string,
  welcomePoints: number = 0
): Promise<{ success: boolean; error?: string }> {
  
  try {
    // Vérifier si les emails sont activés pour cette boutique
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      console.log('Emails désactivés pour cette boutique');
      return { success: false, error: 'Emails désactivés' };
    }

    // Récupérer les informations du client
    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      console.log('Client non trouvé ou sans email');
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    // Préparer les données du template
    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      welcomePoints,
      shopName: settings.shopName || shop,
      shopUrl: `https://${shop}`,
      pointsName: settings.pointsName || 'points'
    };

    // Envoyer l'email
    const result = await sendEmail(
      EmailType.WELCOME,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email de bienvenue envoyé à ${customer.email}`);
    } else {
      console.error(`Erreur envoi email bienvenue:`, result.error);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerWelcomeEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Envoyer un email quand des points sont gagnés
 */
export async function triggerPointsEarnedEmail(
  shop: string,
  customerId: string,
  pointsEarned: number,
  totalPoints: number,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      return { success: false, error: 'Emails désactivés' };
    }

    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      points: pointsEarned,
      totalPoints,
      reason,
      shopName: settings.shopName || shop,
      pointsName: settings.pointsName || 'points'
    };

    const result = await sendEmail(
      EmailType.POINTS_EARNED,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email points gagnés envoyé à ${customer.email} (${pointsEarned} points)`);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerPointsEarnedEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Envoyer un email quand des points sont utilisés
 */
export async function triggerPointsRedeemedEmail(
  shop: string,
  customerId: string,
  pointsRedeemed: number,
  remainingPoints: number,
  rewardDescription?: string
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      return { success: false, error: 'Emails désactivés' };
    }

    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      points: pointsRedeemed,
      remainingPoints,
      rewardDescription,
      shopName: settings.shopName || shop,
      pointsName: settings.pointsName || 'points'
    };

    const result = await sendEmail(
      EmailType.POINTS_REDEEMED,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email points utilisés envoyé à ${customer.email} (${pointsRedeemed} points)`);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerPointsRedeemedEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Envoyer un email de promotion de niveau
 */
export async function triggerLevelUpEmail(
  shop: string,
  customerId: string,
  newLevel: string,
  previousLevel?: string,
  bonusPoints?: number
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      return { success: false, error: 'Emails désactivés' };
    }

    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      newLevel,
      previousLevel,
      bonusPoints,
      shopName: settings.shopName || shop,
      pointsName: settings.pointsName || 'points'
    };

    const result = await sendEmail(
      EmailType.LEVEL_UP,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email promotion niveau envoyé à ${customer.email} (${newLevel})`);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerLevelUpEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Envoyer un email de récompense de parrainage
 */
export async function triggerReferralRewardEmail(
  shop: string,
  customerId: string,
  pointsEarned: number,
  referredCustomerName?: string
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      return { success: false, error: 'Emails désactivés' };
    }

    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      points: pointsEarned,
      referredCustomerName,
      shopName: settings.shopName || shop,
      pointsName: settings.pointsName || 'points'
    };

    const result = await sendEmail(
      EmailType.REFERRAL_REWARD,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email récompense parrainage envoyé à ${customer.email} (${pointsEarned} points)`);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerReferralRewardEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Envoyer un rappel mensuel du solde de points
 */
export async function triggerMonthlyReminderEmail(
  shop: string,
  customerId: string
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const settings = await getSiteSettings(shop);
    if (!settings?.emailNotifications) {
      return { success: false, error: 'Emails désactivés' };
    }

    const customer = await getCustomerById(customerId);
    if (!customer || !customer.email) {
      return { success: false, error: 'Client non trouvé ou sans email' };
    }

    const templateData = {
      customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || customer.email,
      points: customer.points,
      vipLevel: customer.vipLevel,
      shopName: settings.shopName || shop,
      shopUrl: `https://${shop}`,
      pointsName: settings.pointsName || 'points'
    };

    const result = await sendEmail(
      EmailType.MONTHLY_REMINDER,
      {
        to: customer.email,
        toName: templateData.customerName,
        templateData
      }
    );

    if (result.success) {
      console.log(`Email rappel mensuel envoyé à ${customer.email}`);
    }

    return result;

  } catch (error) {
    console.error('Erreur dans triggerMonthlyReminderEmail:', error);
    return { success: false, error: 'Erreur interne' };
  }
}

/**
 * Fonction utilitaire pour déclencher un email basé sur un événement
 */
export async function triggerEmailFromEvent(event: TriggerEvent): Promise<{ success: boolean; error?: string }> {
  const { shop, customerId, eventType, eventData } = event;

  switch (eventType) {
    case EmailType.WELCOME:
      return triggerWelcomeEmail(shop, customerId, eventData.welcomePoints);
    
    case EmailType.POINTS_EARNED:
      return triggerPointsEarnedEmail(
        shop, 
        customerId, 
        eventData.pointsEarned, 
        eventData.totalPoints, 
        eventData.reason
      );
    
    case EmailType.POINTS_REDEEMED:
      return triggerPointsRedeemedEmail(
        shop, 
        customerId, 
        eventData.pointsRedeemed, 
        eventData.remainingPoints, 
        eventData.rewardDescription
      );
    
    case EmailType.LEVEL_UP:
      return triggerLevelUpEmail(
        shop, 
        customerId, 
        eventData.newLevel, 
        eventData.previousLevel, 
        eventData.bonusPoints
      );
    
    case EmailType.REFERRAL_REWARD:
      return triggerReferralRewardEmail(
        shop, 
        customerId, 
        eventData.pointsEarned, 
        eventData.referredCustomerName
      );
    
    case EmailType.MONTHLY_REMINDER:
      return triggerMonthlyReminderEmail(shop, customerId);
    
    default:
      return { success: false, error: 'Type d\'événement non supporté' };
  }
}
