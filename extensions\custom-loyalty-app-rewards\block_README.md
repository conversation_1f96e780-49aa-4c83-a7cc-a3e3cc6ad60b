# 🧩 Blocs Liquid - Custom Loyalty Rewards App

## Vue d'ensemble

Ces blocs Liquid permettent d'intégrer facilement le programme de fidélité dans différentes pages de votre boutique Shopify. Chaque bloc affiche des informations contextuelles et ouvre le widget de fidélité au clic.

---

## 📋 Blocs Disponibles

### 1. `loyalty_product_widget.liquid`
**Page :** Produit  
**Fonction :** Affiche les points à gagner avec le produit  
**Cible :** Encourage l'achat en montrant la récompense

**Fonctionnalités :**
- ✅ Points calculés automatiquement selon le prix
- ✅ Badge VIP pour les clients premium
- ✅ Message différent pour visiteurs/membres
- ✅ Mise à jour en temps réel lors du changement de variante
- ✅ Animation au survol

### 2. `loyalty_checkout_widget.liquid`
**Page :** Checkout/Panier  
**Fonction :** Résumé des points à gagner avec la commande  
**Cible :** Finalise la conversion en montrant la valeur

**Fonctionnalités :**
- ✅ Calcul des points de base + bonus VIP
- ✅ Aperçu du nouveau solde après achat
- ✅ Encouragement à l'inscription pour les visiteurs
- ✅ Design premium avec dégradés
- ✅ Mise à jour automatique du panier

### 3. `loyalty_thankyou_widget.liquid`
**Page :** Thank You (après commande)  
**Fonction :** Célèbre les points gagnés et encourage l'engagement  
**Cible :** Fidélise après l'achat

**Fonctionnalités :**
- ✅ Animation de célébration
- ✅ Affichage des points gagnés avec animation
- ✅ Calcul de la prochaine récompense
- ✅ Invitation au parrainage
- ✅ Message d'opportunité manquée pour les visiteurs

### 4. `loyalty_account_widget.liquid`
**Page :** Espace client  
**Fonction :** Dashboard complet du programme de fidélité  
**Cible :** Hub central pour la gestion du compte

**Fonctionnalités :**
- ✅ Statistiques complètes (points, commandes, dépenses)
- ✅ Barre de progression vers le niveau suivant
- ✅ Actions rapides (récompenses, activité, parrainage)
- ✅ Liste des avantages selon le niveau VIP
- ✅ Design adaptatif et professionnel

---

## 🚀 Installation

### Méthode 1 : Via l'Éditeur de Thème (Recommandé)

1. **Ouvrir l'éditeur de thème** Shopify
2. **Naviguer vers la page** concernée (produit, checkout, etc.)
3. **Cliquer sur "Ajouter une section"**
4. **Sélectionner "Extensions d'application"**
5. **Choisir le bloc** de fidélité approprié
6. **Configurer les paramètres** selon vos besoins

### Méthode 2 : Intégration Manuelle

Si vous préférez intégrer manuellement dans vos templates :

```liquid
<!-- Dans product.liquid -->
{% render 'loyalty_product_widget', product: product %}

<!-- Dans checkout.liquid -->
{% render 'loyalty_checkout_widget', cart: cart %}

<!-- Dans thank-you.liquid -->
{% render 'loyalty_thankyou_widget', order: order %}

<!-- Dans account.liquid -->
{% render 'loyalty_account_widget', customer: customer %}
```

---

## ⚙️ Configuration

### Paramètres Disponibles

Chaque bloc propose des paramètres configurables via l'éditeur de thème :

#### Widget Produit
- **Afficher pour les visiteurs** : Encourage l'inscription
- **Afficher le badge VIP** : Met en valeur le statut premium
- **Titre personnalisé** : Personnalise le message
- **Couleur d'accent** : Adapte aux couleurs du thème

#### Widget Checkout
- **Afficher pour les visiteurs** : Encourage l'inscription
- **Aperçu du nouveau solde** : Montre le solde après achat
- **Titre pour les membres** : Message personnalisé membres
- **Titre pour les visiteurs** : Message personnalisé visiteurs

#### Widget Thank You
- **Afficher pour les visiteurs** : Encourage l'inscription
- **Afficher la prochaine récompense** : Motive l'engagement
- **Invitation au parrainage** : Encourage le parrainage
- **Message de félicitations** : Personnalise la célébration

#### Widget Compte
- **Barre de progression** : Montre l'avancement vers le niveau suivant
- **Afficher les avantages** : Liste les bénéfices du niveau
- **Actions rapides** : Boutons d'accès rapide
- **Titre personnalisé** : Personnalise l'en-tête

---

## 🎨 Personnalisation

### Variables CSS Globales

Tous les blocs respectent ces variables CSS pour une cohérence visuelle :

```css
:root {
  --loyalty-primary: #2E7D32;
  --loyalty-secondary: #4CAF50;
  --loyalty-accent: #FFD700;
  --loyalty-text: #333333;
  --loyalty-text-light: #6c757d;
  --loyalty-background: #f8f9fa;
  --loyalty-border: #e0e0e0;
  --loyalty-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
}
```

### Surcharge des Styles

Pour personnaliser l'apparence, ajoutez du CSS dans votre thème :

```css
/* Personnaliser le widget produit */
.loyalty-product-widget {
  border-radius: 20px;
  background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
}

/* Personnaliser le widget checkout */
.loyalty-checkout-widget {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
}

/* Personnaliser les animations */
.loyalty-product-widget:hover {
  transform: translateY(-5px) scale(1.02);
}
```

---

## 📱 Responsive Design

Tous les blocs sont entièrement responsives avec des breakpoints optimisés :

- **Desktop** (>768px) : Layout complet avec toutes les fonctionnalités
- **Tablet** (768px-480px) : Layout adapté avec éléments réorganisés  
- **Mobile** (<480px) : Layout vertical optimisé pour le tactile

### Points de Rupture

```css
/* Tablet */
@media (max-width: 768px) {
  .loyalty-*-widget {
    padding: 16px;
    margin: 16px 0;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .loyalty-*-widget {
    padding: 12px;
    margin: 12px 0;
  }
}
```

---

## 🔧 Intégration JavaScript

### API Disponible

Les blocs utilisent l'API JavaScript globale du widget :

```javascript
// Ouvrir le widget
window.loyaltyWidget.open();

// Accéder aux données client
window.loyaltyWidget.customerData;

// Écouter les mises à jour
window.addEventListener('loyaltyPointsUpdated', function(event) {
  console.log('Points mis à jour:', event.detail);
});
```

### Événements Personnalisés

Les blocs déclenchent des événements pour l'intégration avancée :

```javascript
// Clic sur un widget
window.addEventListener('loyaltyWidgetClicked', function(event) {
  console.log('Widget cliqué:', event.detail.type);
});

// Mise à jour des points
window.addEventListener('loyaltyPointsCalculated', function(event) {
  console.log('Points calculés:', event.detail.points);
});
```

---

## 🧪 Tests et Validation

### Checklist de Test

Pour chaque bloc, vérifiez :

- [ ] **Affichage correct** sur desktop/mobile
- [ ] **Clic fonctionnel** ouvre le widget
- [ ] **Données correctes** (points, niveaux, etc.)
- [ ] **Responsive** sur différentes tailles d'écran
- [ ] **Performance** (temps de chargement)

### Outils de Test

```javascript
// Tester l'API dans la console
console.log('Widget disponible:', !!window.loyaltyWidget);
console.log('Données client:', window.loyaltyWidget?.customerData);

// Simuler un clic
document.querySelector('.loyalty-*-widget').click();

// Vérifier les événements
window.addEventListener('loyaltyPointsUpdated', console.log);
```

---

## 🚨 Dépannage

### Problèmes Courants

| Problème | Cause | Solution |
|----------|-------|----------|
| Widget ne s'affiche pas | Bloc non ajouté | Vérifier l'éditeur de thème |
| Clic ne fonctionne pas | Widget principal non chargé | Vérifier le bloc principal |
| Styles cassés | Conflit CSS | Vérifier les sélecteurs CSS |
| Points incorrects | Calcul erroné | Vérifier la configuration des points |

### Debug JavaScript

```javascript
// Vérifier l'état des blocs
document.querySelectorAll('[class*="loyalty-"][class*="-widget"]').forEach(widget => {
  console.log('Widget trouvé:', widget.className);
});

// Tester les calculs de points
function testPointsCalculation(price) {
  const points = Math.round(price / 100);
  console.log(`Prix: ${price}€ = ${points} points`);
  return points;
}
```

---

## 📞 Support

### Ressources

- **Documentation complète** : `INTEGRATION_GUIDE.md`
- **Snippets Liquid** : Dossier `snippets/`
- **Page de test** : `test-integration.html`

### Contact

Pour toute assistance technique :
- Vérifiez la console du navigateur pour les erreurs
- Testez avec le thème par défaut pour isoler les conflits
- Fournissez des captures d'écran et logs d'erreur

---

## 🔄 Mises à Jour

### Changelog

**v1.0.0** - Version initiale
- 4 blocs Liquid complets
- Design responsive
- Configuration via éditeur de thème
- Intégration JavaScript complète

### Évolutions Prévues

- **Templates visuels** avec éditeur WYSIWYG
- **A/B testing** des designs
- **Analytics** intégrées
- **Personnalisation avancée** par segment client
