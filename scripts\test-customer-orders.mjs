/**
 * Script de test pour vérifier que les commandes sont bien créées et affichées
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestData() {
  console.log('🧪 Création de données de test pour les commandes...');
  
  try {
    const shop = "test-shop.myshopify.com";
    
    // 1. Créer les paramètres du shop
    await prisma.settings.upsert({
      where: { shop: shop },
      update: {},
      create: {
        shop: shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        pointsName: "Points",
        welcomeMessage: "Welcome to our loyalty program!"
      }
    });

    // 2. Créer un client de test
    const customer = await prisma.customer.create({
      data: {
        customerId: "test-customer-orders-123",
        shop: shop,
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        type: "member",
        points: 150,
        totalSpent: 250.0,
        ordersCount: 2
      }
    });

    console.log(`✅ Client créé: ${customer.firstName} ${customer.lastName} (ID: ${customer.id})`);

    // 3. Créer des commandes de test
    const orders = [
      {
        orderId: "test-order-001",
        customerDbId: customer.id,
        shopifyCustomerId: customer.customerId,
        shop: shop,
        total: 125.50,
        status: "fulfilled",
        paymentStatus: "paid"
      },
      {
        orderId: "test-order-002",
        customerDbId: customer.id,
        shopifyCustomerId: customer.customerId,
        shop: shop,
        total: 89.99,
        status: "pending",
        paymentStatus: "pending"
      },
      {
        orderId: "test-order-003",
        customerDbId: customer.id,
        shopifyCustomerId: customer.customerId,
        shop: shop,
        total: 34.99,
        status: "cancelled",
        paymentStatus: "refunded"
      }
    ];

    for (const orderData of orders) {
      const order = await prisma.order.create({
        data: orderData
      });
      console.log(`✅ Commande créée: ${order.orderId} - ${order.total}€ (${order.status})`);
    }

    // 4. Créer de l'historique de points
    await prisma.pointsHistory.createMany({
      data: [
        {
          ledgerId: customer.id,
          action: "earn",
          points: 125,
          description: "Points earned for order #test-order-001",
          metadata: JSON.stringify({ orderId: "test-order-001" })
        },
        {
          ledgerId: customer.id,
          action: "signup",
          points: 25,
          description: "Welcome bonus",
          metadata: JSON.stringify({ source: "signup" })
        }
      ]
    });

    console.log('✅ Historique des points créé');

    // 5. Vérifier que tout est bien lié
    const customerWithOrders = await prisma.customer.findUnique({
      where: { id: customer.id },
      include: {
        orders: {
          orderBy: { createdAt: "desc" }
        },
        history: {
          orderBy: { timestamp: "desc" }
        }
      }
    });

    console.log('\n📊 Vérification des données:');
    console.log(`   - Client: ${customerWithOrders.firstName} ${customerWithOrders.lastName}`);
    console.log(`   - Points: ${customerWithOrders.points}`);
    console.log(`   - Total dépensé: ${customerWithOrders.totalSpent}€`);
    console.log(`   - Nombre de commandes (compteur): ${customerWithOrders.ordersCount}`);
    console.log(`   - Commandes en base: ${customerWithOrders.orders.length}`);
    console.log(`   - Historique des points: ${customerWithOrders.history.length} entrées`);

    if (customerWithOrders.orders.length > 0) {
      console.log('\n📋 Commandes:');
      customerWithOrders.orders.forEach(order => {
        console.log(`   - ${order.orderId}: ${order.total}€ (${order.status}/${order.paymentStatus})`);
      });
    }

    if (customerWithOrders.history.length > 0) {
      console.log('\n📈 Historique des points:');
      customerWithOrders.history.forEach(entry => {
        console.log(`   - ${entry.action}: ${entry.points} points - ${entry.description}`);
      });
    }

    return customer;

  } catch (error) {
    console.error('❌ Erreur lors de la création des données de test:', error);
    throw error;
  }
}

async function testOrdersDisplay() {
  console.log('\n🧪 Test de récupération des données client...');
  
  try {
    // Importer la fonction getCustomerById
    const { getCustomerById } = await import('../app/models/Customer.server.ts');
    
    const customer = await getCustomerById("test-customer-orders-123", "test-shop.myshopify.com");
    
    if (customer) {
      console.log('✅ Client récupéré via getCustomerById:');
      console.log(`   - Nom: ${customer.firstName} ${customer.lastName}`);
      console.log(`   - Points: ${customer.points}`);
      console.log(`   - Commandes: ${customer.orders?.length || 0}`);
      console.log(`   - Historique: ${customer.history?.length || 0}`);
      
      if (customer.orders && customer.orders.length > 0) {
        console.log('\n📋 Commandes récupérées:');
        customer.orders.forEach(order => {
          console.log(`   - ${order.orderId}: ${order.total}€ (${order.status})`);
        });
      } else {
        console.log('❌ Aucune commande récupérée par getCustomerById');
      }
    } else {
      console.log('❌ Client non trouvé par getCustomerById');
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

async function cleanup() {
  console.log('\n🧹 Nettoyage des données de test...');
  
  try {
    // Supprimer dans l'ordre inverse des dépendances
    await prisma.pointsHistory.deleteMany({
      where: {
        OR: [
          { metadata: { contains: "test-order-" } },
          { metadata: { contains: "signup" } }
        ]
      }
    });

    await prisma.order.deleteMany({
      where: {
        orderId: { startsWith: "test-order-" }
      }
    });

    await prisma.customer.deleteMany({
      where: {
        customerId: "test-customer-orders-123"
      }
    });

    await prisma.settings.deleteMany({
      where: {
        shop: "test-shop.myshopify.com"
      }
    });

    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

async function main() {
  try {
    await cleanup(); // Nettoyer d'abord
    const customer = await createTestData();
    await testOrdersDisplay();
    
    console.log('\n📋 Données de test créées. Vous pouvez maintenant tester la page client :');
    console.log(`   - URL: /app/customers/${customer.id}`);
    console.log(`   - Client: ${customer.firstName} ${customer.lastName}`);
    console.log(`   - Email: ${customer.email}`);
    
    console.log('\n🔍 Voulez-vous nettoyer les données de test ? (Ctrl+C pour garder)');
    
    // Attendre 10 secondes puis nettoyer automatiquement
    setTimeout(async () => {
      await cleanup();
      await prisma.$disconnect();
      process.exit(0);
    }, 10000);
    
  } catch (error) {
    console.error('❌ Erreur:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

main();
