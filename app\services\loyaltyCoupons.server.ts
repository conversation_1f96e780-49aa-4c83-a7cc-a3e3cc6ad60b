import prisma from "../db.server";
import { createLoyaltyDiscountCode, CreateDiscountResult } from "./shopifyDiscounts.server";
import { getPointsSettings } from "../models/PointsSettings.server";
import { triggerPointsRedeemedEmail } from "./emailTriggers.server";

export interface CouponRedemptionData {
  customerId: string;
  shop: string;
  pointsToSpend: number;
  wayToRedeemId?: string;
}

export interface CouponResult {
  success: boolean;
  coupon?: {
    id: string;
    code: string;
    value: number;
    expiresAt: Date;
    pointsSpent: number;
  };
  error?: string;
}

/**
 * Créer un coupon de fidélité en échangeant des points
 */
export async function createLoyaltyCoupon(
  request: Request,
  redemptionData: CouponRedemptionData
): Promise<CouponResult> {
  try {
    const { customerId, shop, pointsToSpend, wayToRedeemId } = redemptionData;

    // Récupérer les paramètres du programme
    const settings = await getPointsSettings(shop);
    if (!settings) {
      return {
        success: false,
        error: "Paramètres du programme non trouvés"
      };
    }

    // Vérifier les points minimum
    if (pointsToSpend < settings.minimumPoints) {
      return {
        success: false,
        error: `Minimum ${settings.minimumPoints} points requis`
      };
    }

    // Récupérer les données du client
    const customer = await prisma.customer.findUnique({
      where: {
        customerId_shop: {
          customerId: customerId,
          shop: shop
        }
      }
    });

    if (!customer) {
      return {
        success: false,
        error: "Client non trouvé"
      };
    }

    // Vérifier si le client a assez de points
    if (customer.points < pointsToSpend) {
      return {
        success: false,
        error: "Points insuffisants"
      };
    }

    // Créer le code de réduction Shopify
    const discountResult: CreateDiscountResult = await createLoyaltyDiscountCode(
      request,
      pointsToSpend,
      settings.redemptionRate,
      30 // 30 jours d'expiration
    );

    if (!discountResult.success || !discountResult.code) {
      return {
        success: false,
        error: discountResult.error || "Erreur lors de la création du code de réduction"
      };
    }

    // Calculer la valeur du coupon
    const couponValue = Math.round((pointsToSpend / settings.redemptionRate) * 100) / 100;

    // Date d'expiration (30 jours)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Déduire les points du client
    const newPointsBalance = customer.points - pointsToSpend;
    await prisma.customer.update({
      where: { id: customer.id },
      data: { points: newPointsBalance }
    });

    // Enregistrer le coupon dans la base de données
    const reward = await prisma.reward.create({
      data: {
        customerId: customerId,
        shop: shop,
        type: "discount",
        name: `Coupon de ${couponValue}€`,
        value: couponValue,
        pointsCost: pointsToSpend,
        code: discountResult.code,
        status: "active",
        expiresAt: expiresAt
      }
    });

    // Enregistrer l'historique des points
    await prisma.pointsHistory.create({
      data: {
        ledgerId: customer.id,
        action: "redeem",
        points: -pointsToSpend,
        description: `Échange contre coupon de ${couponValue}€`,
        metadata: JSON.stringify({
          couponCode: discountResult.code,
          couponValue: couponValue,
          wayToRedeemId: wayToRedeemId
        })
      }
    });

    // Déclencher l'email de points utilisés (asynchrone, ne pas attendre)
    triggerPointsRedeemedEmail(
      shop,
      customer.id,
      pointsToSpend,
      newPointsBalance,
      `Coupon de ${couponValue}€`
    ).catch(error => {
      console.error('Erreur envoi email points utilisés:', error);
    });

    return {
      success: true,
      coupon: {
        id: reward.id,
        code: discountResult.code,
        value: couponValue,
        expiresAt: expiresAt,
        pointsSpent: pointsToSpend
      }
    };

  } catch (error) {
    console.error("Error creating loyalty coupon:", error);
    return {
      success: false,
      error: "Erreur lors de la création du coupon"
    };
  }
}

/**
 * Récupérer les coupons d'un client
 */
export async function getCustomerCoupons(customerId: string, shop: string) {
  try {
    const rewards = await prisma.reward.findMany({
      where: {
        customerId: customerId,
        shop: shop,
        type: "discount",
        status: "active",
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    return rewards.map(reward => ({
      id: reward.id,
      code: reward.code,
      value: reward.value,
      pointsCost: reward.pointsCost,
      expiresAt: reward.expiresAt,
      createdAt: reward.createdAt
    }));

  } catch (error) {
    console.error("Error fetching customer coupons:", error);
    return [];
  }
}

/**
 * Marquer un coupon comme utilisé
 */
export async function markCouponAsUsed(couponId: string, shop: string) {
  try {
    await prisma.reward.update({
      where: {
        id: couponId,
        shop: shop
      },
      data: {
        status: "used",
        usedAt: new Date()
      }
    });

    return true;
  } catch (error) {
    console.error("Error marking coupon as used:", error);
    return false;
  }
}

/**
 * Calculer la valeur d'un coupon basé sur les points
 */
export async function calculateCouponValue(shop: string, points: number): Promise<number> {
  try {
    const settings = await getPointsSettings(shop);
    if (!settings) {
      return 0;
    }

    // Valeur = points / taux de conversion
    return Math.round((points / settings.redemptionRate) * 100) / 100;
  } catch (error) {
    console.error("Error calculating coupon value:", error);
    return 0;
  }
}
