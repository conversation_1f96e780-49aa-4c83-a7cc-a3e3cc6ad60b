import prisma from "../db.server";

export interface VIPLevel {
  id: string;
  shop: string;
  name: string;
  threshold: number;
  pointsMultiplier: number;
  benefits: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface VIPProgramSettings {
  shop: string;
  enabled: boolean;
  retentionPeriod: number;
  evaluationPeriod: number;
}

export interface CreateVIPLevelData {
  shop: string;
  name: string;
  threshold: number;
  pointsMultiplier: number;
  benefits: string;
}

export interface UpdateVIPLevelData {
  name?: string;
  threshold?: number;
  pointsMultiplier?: number;
  benefits?: string;
}

/**
 * BROpoints level configuration
 */
export const BROPOINTS_LEVELS = {
  BRO: { threshold: 0, multiplier: 1.0, benefits: "Base earning rate" },
  EhrenBRO: { threshold: 1000, multiplier: 1.25, benefits: "25% bonus points on all purchases" },
  BesterBRO: { threshold: 5000, multiplier: 1.5, benefits: "50% bonus points on all purchases\nPriority customer support" }
};

/**
 * Initialize BROpoints levels for a shop
 */
export async function initializeBROPointsLevels(shop: string): Promise<VIPLevel[]> {
  try {
    // Check if levels already exist
    const existingLevels = await prisma.vIPLevel.findMany({
      where: { shop }
    });

    if (existingLevels.length > 0) {
      console.log(`BROpoints levels already exist for shop: ${shop}`);
      return existingLevels;
    }

    // Create the three BROpoints levels
    const levelsToCreate = [
      {
        shop,
        name: "BRO",
        threshold: BROPOINTS_LEVELS.BRO.threshold,
        pointsMultiplier: BROPOINTS_LEVELS.BRO.multiplier,
        benefits: BROPOINTS_LEVELS.BRO.benefits
      },
      {
        shop,
        name: "EhrenBRO",
        threshold: BROPOINTS_LEVELS.EhrenBRO.threshold,
        pointsMultiplier: BROPOINTS_LEVELS.EhrenBRO.multiplier,
        benefits: BROPOINTS_LEVELS.EhrenBRO.benefits
      },
      {
        shop,
        name: "BesterBRO",
        threshold: BROPOINTS_LEVELS.BesterBRO.threshold,
        pointsMultiplier: BROPOINTS_LEVELS.BesterBRO.multiplier,
        benefits: BROPOINTS_LEVELS.BesterBRO.benefits
      }
    ];

    const createdLevels = await Promise.all(
      levelsToCreate.map(level => 
        prisma.vIPLevel.create({ data: level })
      )
    );

    console.log(`Created ${createdLevels.length} BROpoints levels for shop: ${shop}`);
    return createdLevels;

  } catch (error) {
    console.error("Error initializing BROpoints levels:", error);
    throw error;
  }
}

/**
 * Get all VIP levels for a shop
 */
export async function getVIPLevels(shop: string): Promise<VIPLevel[]> {
  try {
    return await prisma.vIPLevel.findMany({
      where: { shop },
      orderBy: { threshold: 'asc' }
    });
  } catch (error) {
    console.error("Error fetching VIP levels:", error);
    return [];
  }
}

/**
 * Get a specific VIP level by ID
 */
export async function getVIPLevelById(id: string): Promise<VIPLevel | null> {
  try {
    return await prisma.vIPLevel.findUnique({
      where: { id }
    });
  } catch (error) {
    console.error("Error fetching VIP level by ID:", error);
    return null;
  }
}

/**
 * Create a new VIP level
 */
export async function createVIPLevel(data: CreateVIPLevelData): Promise<VIPLevel | null> {
  try {
    return await prisma.vIPLevel.create({
      data
    });
  } catch (error) {
    console.error("Error creating VIP level:", error);
    return null;
  }
}

/**
 * Update a VIP level
 */
export async function updateVIPLevel(id: string, data: UpdateVIPLevelData): Promise<VIPLevel | null> {
  try {
    return await prisma.vIPLevel.update({
      where: { id },
      data
    });
  } catch (error) {
    console.error("Error updating VIP level:", error);
    return null;
  }
}

/**
 * Delete a VIP level
 */
export async function deleteVIPLevel(id: string): Promise<boolean> {
  try {
    await prisma.vIPLevel.delete({
      where: { id }
    });
    return true;
  } catch (error) {
    console.error("Error deleting VIP level:", error);
    return false;
  }
}



/**
 * Calculate the appropriate VIP level for a customer based on their points
 */
export async function calculateCustomerLevel(shop: string, points: number): Promise<VIPLevel | null> {
  try {
    const levels = await getVIPLevels(shop);

    // Find the highest level the customer qualifies for
    let qualifiedLevel: VIPLevel | null = null;

    for (const level of levels) {
      if (points >= level.threshold) {
        qualifiedLevel = level;
      } else {
        break; // Levels are ordered by threshold, so we can break here
      }
    }

    return qualifiedLevel;
  } catch (error) {
    console.error("Error calculating customer level:", error);
    return null;
  }
}

/**
 * Get the next level for a customer
 */
export async function getNextLevel(shop: string, currentPoints: number): Promise<VIPLevel | null> {
  try {
    const levels = await getVIPLevels(shop);
    
    // Find the next level the customer can reach
    for (const level of levels) {
      if (currentPoints < level.threshold) {
        return level;
      }
    }
    
    return null; // Customer is already at the highest level
  } catch (error) {
    console.error("Error getting next level:", error);
    return null;
  }
}

/**
 * Get level statistics for a shop
 */
export async function getLevelStatistics(shop: string): Promise<Record<string, number>> {
  try {
    const levels = await getVIPLevels(shop);
    const stats: Record<string, number> = {};

    for (const level of levels) {
      const count = await prisma.customer.count({
        where: {
          shop,
          vipLevel: level.name
        }
      });
      stats[level.name] = count;
    }

    return stats;
  } catch (error) {
    console.error("Error getting level statistics:", error);
    return {};
  }
}

/**
 * Get VIP program settings for a shop
 */
export async function getVIPProgramSettings(shop: string): Promise<VIPProgramSettings> {
  try {
    const settings = await prisma.settings.findUnique({
      where: { shop },
      select: {
        vipProgramEnabled: true,
        vipRetentionPeriod: true,
        vipEvaluationPeriod: true
      }
    });

    return {
      shop,
      enabled: settings?.vipProgramEnabled ?? true,
      retentionPeriod: settings?.vipRetentionPeriod ?? 365,
      evaluationPeriod: settings?.vipEvaluationPeriod ?? 90
    };
  } catch (error) {
    console.error("Error getting VIP program settings:", error);
    return {
      shop,
      enabled: true,
      retentionPeriod: 365,
      evaluationPeriod: 90
    };
  }
}

/**
 * Update VIP program settings
 */
export async function updateVIPProgramSettings(shop: string, settings: Partial<VIPProgramSettings>): Promise<boolean> {
  try {
    await prisma.settings.upsert({
      where: { shop },
      create: {
        shop,
        vipProgramEnabled: settings.enabled ?? true,
        vipRetentionPeriod: settings.retentionPeriod ?? 365,
        vipEvaluationPeriod: settings.evaluationPeriod ?? 90
      },
      update: {
        ...(settings.enabled !== undefined && { vipProgramEnabled: settings.enabled }),
        ...(settings.retentionPeriod !== undefined && { vipRetentionPeriod: settings.retentionPeriod }),
        ...(settings.evaluationPeriod !== undefined && { vipEvaluationPeriod: settings.evaluationPeriod })
      }
    });

    return true;
  } catch (error) {
    console.error("Error updating VIP program settings:", error);
    return false;
  }
}
