/**
 * Test simple pour vérifier la requête getCustomerById
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testCustomerQuery() {
  console.log('🧪 Test de la requête client avec commandes...');
  
  try {
    // Créer des données de test rapidement
    const shop = "test-query.myshopify.com";
    
    await prisma.settings.upsert({
      where: { shop },
      update: {},
      create: {
        shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        pointsName: "Points",
        welcomeMessage: "Welcome!"
      }
    });

    const customer = await prisma.customer.create({
      data: {
        customerId: "test-query-456",
        shop,
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        type: "member",
        points: 100,
        totalSpent: 150.0,
        ordersCount: 2
      }
    });

    // Créer 2 commandes
    await prisma.order.createMany({
      data: [
        {
          orderId: "query-order-1",
          customerDbId: customer.id,
          shopifyCustomerId: customer.customerId,
          shop,
          total: 75.0,
          status: "fulfilled",
          paymentStatus: "paid"
        },
        {
          orderId: "query-order-2", 
          customerDbId: customer.id,
          shopifyCustomerId: customer.customerId,
          shop,
          total: 75.0,
          status: "pending",
          paymentStatus: "pending"
        }
      ]
    });

    console.log(`✅ Données créées pour le client ${customer.customerId}`);

    // Test 1: Requête simple
    const simpleCustomer = await prisma.customer.findFirst({
      where: { customerId: customer.customerId, shop }
    });

    console.log(`📊 Client simple: ${simpleCustomer ? 'trouvé' : 'non trouvé'}`);

    // Test 2: Requête avec commandes (comme dans getCustomerById)
    const customerWithOrders = await prisma.customer.findFirst({
      where: { customerId: customer.customerId, shop },
      include: {
        history: {
          orderBy: { timestamp: "desc" },
          take: 50
        },
        referrals: {
          include: {
            referred: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        referredBy: {
          include: {
            referrer: {
              select: {
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        orders: {
          orderBy: { createdAt: "desc" },
          take: 20
        }
      }
    });

    if (customerWithOrders) {
      console.log('✅ Client avec relations trouvé:');
      console.log(`   - Nom: ${customerWithOrders.firstName} ${customerWithOrders.lastName}`);
      console.log(`   - Points: ${customerWithOrders.points}`);
      console.log(`   - OrdersCount: ${customerWithOrders.ordersCount}`);
      console.log(`   - Commandes récupérées: ${customerWithOrders.orders.length}`);
      console.log(`   - Historique: ${customerWithOrders.history.length}`);
      console.log(`   - Parrainages: ${customerWithOrders.referrals.length}`);

      if (customerWithOrders.orders.length > 0) {
        console.log('\n📋 Détails des commandes:');
        customerWithOrders.orders.forEach(order => {
          console.log(`   - ${order.orderId}: ${order.total}€ (${order.status}/${order.paymentStatus})`);
          console.log(`     CustomerDbId: ${order.customerDbId}`);
          console.log(`     ShopifyCustomerId: ${order.shopifyCustomerId}`);
        });
      } else {
        console.log('❌ Aucune commande récupérée malgré la création');
      }
    } else {
      console.log('❌ Client avec relations non trouvé');
    }

    // Test 3: Vérifier les commandes directement
    const directOrders = await prisma.order.findMany({
      where: { customerDbId: customer.id }
    });

    console.log(`\n🔍 Commandes directes: ${directOrders.length} trouvées`);
    directOrders.forEach(order => {
      console.log(`   - ${order.orderId}: ${order.total}€ (customerDbId: ${order.customerDbId})`);
    });

    // Nettoyage
    await prisma.order.deleteMany({
      where: { orderId: { startsWith: "query-order-" } }
    });
    await prisma.customer.delete({ where: { id: customer.id } });
    await prisma.settings.delete({ where: { shop } });

    console.log('\n✅ Test terminé et nettoyé');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCustomerQuery();
