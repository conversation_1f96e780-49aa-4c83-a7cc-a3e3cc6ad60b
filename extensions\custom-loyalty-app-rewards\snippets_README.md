# Snippets Liquid - Custom Loyalty Rewards App

Ces snippets Liquid facilitent l'intégration du programme de fidélité dans votre thème Shopify sans utiliser le bouton flottant.

## 📋 Snippets Disponibles

### 1. `loyalty-menu-link.liquid`
**Usage :** Lien de menu pour ouvrir le hub de fidélité

```liquid
<!-- Usage basique -->
{% render 'loyalty-menu-link' %}

<!-- Avec paramètres personnalisés -->
{% render 'loyalty-menu-link', 
   text: 'Mon Programme', 
   show_points: true, 
   class: 'nav-link' %}
```

**Paramètres :**
- `text` : Texte du lien (défaut: "Club BROpoints")
- `show_points` : Afficher le solde de points (défaut: true)
- `class` : Classes CSS supplémentaires

### 2. `loyalty-product-points.liquid`
**Usage :** Affichage des points à gagner sur les pages produit

```liquid
<!-- Usage basique -->
{% render 'loyalty-product-points', product: product %}

<!-- Avec variante spécifique -->
{% render 'loyalty-product-points', 
   product: product, 
   variant: product.selected_or_first_available_variant,
   show_member_only: true %}
```

**Paramètres :**
- `product` : Objet produit Shopify (requis)
- `variant` : Variante spécifique (optionnel)
- `show_member_only` : Afficher le texte "membre uniquement" (défaut: false)

### 3. `loyalty-account-section.liquid`
**Usage :** Section complète pour l'espace client

```liquid
<!-- Usage basique -->
{% render 'loyalty-account-section' %}

<!-- Avec paramètres personnalisés -->
{% render 'loyalty-account-section', 
   title: 'Mes Récompenses', 
   show_summary: true, 
   show_activity: true %}
```

**Paramètres :**
- `title` : Titre de la section (défaut: "Programme de Fidélité")
- `show_summary` : Afficher le résumé (défaut: true)
- `show_activity` : Afficher le lien vers l'activité (défaut: true)

## 🚀 Installation

### Étape 1 : Configuration du Widget
1. Dans l'éditeur de thème Shopify, allez dans **Personnaliser**
2. Ajoutez le bloc **Custom Rewards App** 
3. Activez **"Masquer le bouton flottant"**

### Étape 2 : Copier les Snippets
1. Copiez les fichiers `.liquid` dans le dossier `snippets/` de votre thème
2. Les snippets sont maintenant disponibles avec `{% render 'nom-du-snippet' %}`

### Étape 3 : Intégration dans le Thème

#### Menu Principal (`header.liquid` ou `navigation.liquid`)
```liquid
<!-- Dans votre menu de navigation -->
<nav class="main-navigation">
  <a href="/">Accueil</a>
  <a href="/collections">Boutique</a>
  {% render 'loyalty-menu-link' %}
  <a href="/pages/contact">Contact</a>
</nav>
```

#### Page Produit (`product.liquid`)
```liquid
<!-- Après le prix du produit -->
<div class="product-price">
  {{ product.price | money }}
</div>

{% render 'loyalty-product-points', product: product %}

<!-- Boutons d'achat -->
<form action="/cart/add" method="post">
  <!-- ... -->
</form>
```

#### Espace Client (`account.liquid` ou `customer/account.liquid`)
```liquid
<!-- Section fidélité dans l'espace client -->
<div class="customer-account">
  <h2>Mon Compte</h2>
  
  {% render 'loyalty-account-section' %}
  
  <!-- Autres sections du compte -->
  <div class="account-orders">
    <!-- ... -->
  </div>
</div>
```

## 🎨 Personnalisation CSS

Tous les snippets incluent des styles CSS de base. Pour personnaliser :

### Variables CSS Globales
```css
:root {
  --loyalty-primary: #2E7D32;
  --loyalty-secondary: #4CAF50;
  --loyalty-text: #212121;
  --loyalty-border: #e0e0e0;
}
```

### Surcharger les Styles
```css
/* Personnaliser le lien de menu */
.loyalty-menu-link {
  font-weight: bold;
  text-transform: uppercase;
}

/* Personnaliser l'affichage produit */
.loyalty-product-points {
  border: 2px solid var(--loyalty-primary);
  background: linear-gradient(45deg, #f8f9fa, #ffffff);
}

/* Personnaliser la section compte */
.loyalty-account-section {
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}
```

## 📱 Responsive Design

Tous les snippets sont responsive par défaut. Personnalisations mobiles :

```css
@media (max-width: 768px) {
  .loyalty-menu-link {
    font-size: 14px;
  }
  
  .loyalty-product-points {
    padding: 12px;
    margin: 12px 0;
  }
  
  .loyalty-account-section {
    padding: 16px;
  }
}
```

## 🔧 JavaScript API

Les snippets utilisent l'API JavaScript du widget :

```javascript
// Ouvrir le hub
window.loyaltyWidget.open();

// Fermer le hub
window.loyaltyWidget.close();

// Basculer l'état
window.loyaltyWidget.toggle();

// Ouvrir une section spécifique
window.loyaltyWidget.open();
setTimeout(() => {
  window.loyaltyWidget.showYourRewardsSection();
}, 500);
```

## 🔄 Synchronisation des Données

Les snippets se synchronisent automatiquement avec les données du widget :

```javascript
// Événement déclenché lors de la mise à jour des points
window.addEventListener('loyaltyPointsUpdated', function(event) {
  console.log('Points mis à jour:', event.detail.points);
});

// Accéder aux données client
if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
  const points = window.loyaltyWidget.customerData.points;
  const level = window.loyaltyWidget.customerData.vipLevel;
}
```

## ✅ Vérification

Pour vérifier que tout fonctionne :

1. **Console du navigateur :**
   ```javascript
   console.log('Widget:', window.loyaltyWidget);
   console.log('Données:', window.loyaltyWidget?.customerData);
   ```

2. **Test des liens :** Cliquez sur les liens pour ouvrir le hub

3. **Test responsive :** Vérifiez sur mobile et desktop

4. **Test des points :** Vérifiez l'affichage du solde

## 🚨 Dépannage

### Problèmes Courants

1. **Snippet non trouvé**
   - Vérifiez que le fichier est dans `snippets/`
   - Vérifiez l'orthographe du nom

2. **Styles non appliqués**
   - Vérifiez les conflits CSS avec le thème
   - Utilisez des sélecteurs plus spécifiques

3. **JavaScript non fonctionnel**
   - Vérifiez que le widget est chargé
   - Attendez le chargement complet de la page

### Support

Pour toute assistance :
- Vérifiez la console du navigateur pour les erreurs
- Testez avec le thème par défaut
- Contactez l'équipe de développement
