/**
 * Test utilities for the BROpoints level system
 * These functions help verify that the level system is working correctly
 */

import { initializeBROPointsLevels, getVIPLevels, calculateCustomerLevel } from "../models/VIPLevel.server";
import { checkAndPromoteCustomer, getCustomerLevelInfo } from "../services/levelPromotion.server";
import { awardPointsForOrder } from "../services/pointsService.server";
import prisma from "../db.server";

export interface LevelTestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Test basic level configuration
 */
export async function testLevelConfiguration(shop: string): Promise<LevelTestResult> {
  try {
    // Initialize levels
    await initializeBROPointsLevels(shop);
    
    // Get levels
    const levels = await getVIPLevels(shop);
    
    // Verify we have the correct 3 levels
    if (levels.length !== 3) {
      return {
        success: false,
        message: `Expected 3 levels, got ${levels.length}`,
        details: levels
      };
    }
    
    // Verify level names and thresholds
    const expectedLevels = [
      { name: '<PERSON><PERSON>', threshold: 0, multiplier: 1.0 },
      { name: '<PERSON><PERSON><PERSON><PERSON>', threshold: 1000, multiplier: 1.25 },
      { name: 'Bester<PERSON><PERSON>', threshold: 5000, multiplier: 1.5 }
    ];
    
    for (let i = 0; i < expectedLevels.length; i++) {
      const expected = expectedLevels[i];
      const actual = levels[i];
      
      if (actual.name !== expected.name || 
          actual.threshold !== expected.threshold || 
          actual.pointsMultiplier !== expected.multiplier) {
        return {
          success: false,
          message: `Level ${i} mismatch`,
          details: { expected, actual }
        };
      }
    }
    
    return {
      success: true,
      message: "Level configuration is correct",
      details: levels
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Error testing level configuration: ${error}`,
      details: error
    };
  }
}

/**
 * Test level calculation logic
 */
export async function testLevelCalculation(shop: string): Promise<LevelTestResult> {
  try {
    await initializeBROPointsLevels(shop);
    
    const testCases = [
      { points: 0, expectedLevel: 'BRO' },
      { points: 500, expectedLevel: 'BRO' },
      { points: 999, expectedLevel: 'BRO' },
      { points: 1000, expectedLevel: 'EhrenBRO' },
      { points: 2500, expectedLevel: 'EhrenBRO' },
      { points: 4999, expectedLevel: 'EhrenBRO' },
      { points: 5000, expectedLevel: 'BesterBRO' },
      { points: 10000, expectedLevel: 'BesterBRO' }
    ];
    
    for (const testCase of testCases) {
      const level = await calculateCustomerLevel(shop, testCase.points);
      
      if (!level || level.name !== testCase.expectedLevel) {
        return {
          success: false,
          message: `Level calculation failed for ${testCase.points} points`,
          details: { 
            expected: testCase.expectedLevel, 
            actual: level?.name || 'null',
            testCase 
          }
        };
      }
    }
    
    return {
      success: true,
      message: "Level calculation logic is correct",
      details: testCases
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Error testing level calculation: ${error}`,
      details: error
    };
  }
}

/**
 * Test customer level promotion
 */
export async function testCustomerPromotion(shop: string, customerId: string): Promise<LevelTestResult> {
  try {
    // Create or get customer
    const customer = await prisma.customer.upsert({
      where: {
        customerId_shop: { customerId, shop }
      },
      create: {
        customerId,
        shop,
        points: 0,
        type: "member"
      },
      update: {
        points: 0 // Reset points for test
      }
    });
    
    // Test promotion scenarios
    const scenarios = [
      { points: 500, expectedLevel: 'BRO', shouldPromote: false },
      { points: 1000, expectedLevel: 'EhrenBRO', shouldPromote: true },
      { points: 5000, expectedLevel: 'BesterBRO', shouldPromote: true }
    ];
    
    for (const scenario of scenarios) {
      // Update customer points
      await prisma.customer.update({
        where: { id: customer.id },
        data: { points: scenario.points }
      });
      
      // Check promotion
      const result = await checkAndPromoteCustomer(customerId, shop);
      
      if (result.newLevel !== scenario.expectedLevel) {
        return {
          success: false,
          message: `Promotion test failed for ${scenario.points} points`,
          details: { 
            expected: scenario.expectedLevel, 
            actual: result.newLevel,
            scenario,
            result
          }
        };
      }
    }
    
    return {
      success: true,
      message: "Customer promotion logic is working correctly",
      details: scenarios
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Error testing customer promotion: ${error}`,
      details: error
    };
  }
}

/**
 * Test points multiplier application
 */
export async function testPointsMultiplier(shop: string, customerId: string): Promise<LevelTestResult> {
  try {
    // Set customer to EhrenBRO level (1.25x multiplier)
    const customer = await prisma.customer.upsert({
      where: {
        customerId_shop: { customerId, shop }
      },
      create: {
        customerId,
        shop,
        points: 1000, // EhrenBRO level
        type: "member",
        vipLevel: "EhrenBRO"
      },
      update: {
        points: 1000,
        vipLevel: "EhrenBRO"
      }
    });
    
    // Award points for a test order
    const orderAmount = 100; // Should give 100 base points (1 point per euro)
    const expectedBasePoints = 100;
    const expectedMultiplier = 1.25;
    const expectedFinalPoints = Math.round(expectedBasePoints * expectedMultiplier); // 125 points
    
    const result = await awardPointsForOrder(shop, customerId, "test-order-123", orderAmount);
    
    if (!result) {
      return {
        success: false,
        message: "Failed to award points for test order",
        details: null
      };
    }
    
    if (result.basePoints !== expectedBasePoints || 
        result.multiplier !== expectedMultiplier || 
        result.points !== expectedFinalPoints) {
      return {
        success: false,
        message: "Points multiplier calculation is incorrect",
        details: {
          expected: { basePoints: expectedBasePoints, multiplier: expectedMultiplier, finalPoints: expectedFinalPoints },
          actual: { basePoints: result.basePoints, multiplier: result.multiplier, finalPoints: result.points }
        }
      };
    }
    
    return {
      success: true,
      message: "Points multiplier is working correctly",
      details: result
    };
    
  } catch (error) {
    return {
      success: false,
      message: `Error testing points multiplier: ${error}`,
      details: error
    };
  }
}

/**
 * Run all level system tests
 */
export async function runAllLevelTests(shop: string, testCustomerId: string = "test-customer-123"): Promise<LevelTestResult[]> {
  const results: LevelTestResult[] = [];
  
  console.log("🧪 Running BROpoints Level System Tests...");
  
  // Test 1: Level Configuration
  console.log("Testing level configuration...");
  results.push(await testLevelConfiguration(shop));
  
  // Test 2: Level Calculation
  console.log("Testing level calculation logic...");
  results.push(await testLevelCalculation(shop));
  
  // Test 3: Customer Promotion
  console.log("Testing customer promotion...");
  results.push(await testCustomerPromotion(shop, testCustomerId));
  
  // Test 4: Points Multiplier
  console.log("Testing points multiplier...");
  results.push(await testPointsMultiplier(shop, testCustomerId));
  
  // Summary
  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log(`✅ Tests completed: ${passedTests}/${totalTests} passed`);
  
  return results;
}
