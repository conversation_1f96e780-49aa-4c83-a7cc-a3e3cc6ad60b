import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Layout,
  Card,
  Button,
  BlockStack,
  Text,
  Banner,
  List,
  Badge,
} from "@shopify/polaris";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { runAllLevelTests, type LevelTestResult } from "app/utils/levelSystemTest.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  return json({ 
    shop: session.shop,
    testResults: null 
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  
  try {
    const testResults = await runAllLevelTests(shop);
    
    return json({ 
      shop,
      testResults,
      success: true 
    });
  } catch (error) {
    console.error("Error running level tests:", error);
    return json({ 
      shop,
      testResults: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default function TestLevels() {
  const { shop, testResults, success, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigation = useNavigation();
  
  const isRunning = navigation.state === "submitting";

  const handleRunTests = () => {
    submit({}, { method: "post" });
  };

  const getTestStatusBadge = (result: LevelTestResult) => {
    return result.success ? 
      <Badge tone="success">Passed</Badge> : 
      <Badge tone="critical">Failed</Badge>;
  };

  return (
    <AdminLayout title="Level System Tests">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Banner tone="info">
              <Text as="p" variant="bodyMd">
                This page allows you to test the BROpoints level system to ensure it's working correctly.
                The tests verify level configuration, calculation logic, customer promotion, and points multipliers.
              </Text>
            </Banner>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">BROpoints Level System Tests</Text>
                
                <Text as="p" variant="bodyMd">
                  Shop: <Text as="span" variant="bodyMd" fontWeight="bold">{shop}</Text>
                </Text>
                
                <Button 
                  variant="primary" 
                  onClick={handleRunTests}
                  loading={isRunning}
                  disabled={isRunning}
                >
                  {isRunning ? "Running Tests..." : "Run All Tests"}
                </Button>
              </BlockStack>
            </Card>

            {error && (
              <Banner tone="critical">
                <Text as="p" variant="bodyMd">
                  Error running tests: {error}
                </Text>
              </Banner>
            )}

            {testResults && (
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">Test Results</Text>
                  
                  {testResults.map((result, index) => (
                    <div key={index} style={{ 
                      padding: "16px", 
                      border: "1px solid #e0e0e0", 
                      borderRadius: "8px",
                      backgroundColor: result.success ? "#f8fff8" : "#fff8f8"
                    }}>
                      <BlockStack gap="200">
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                          <Text as="h3" variant="headingSm">Test {index + 1}</Text>
                          {getTestStatusBadge(result)}
                        </div>
                        
                        <Text as="p" variant="bodyMd">
                          {result.message}
                        </Text>
                        
                        {result.details && (
                          <details>
                            <summary style={{ cursor: "pointer", color: "#2E7D32" }}>
                              View Details
                            </summary>
                            <pre style={{ 
                              background: "#f5f5f5", 
                              padding: "12px", 
                              borderRadius: "4px", 
                              fontSize: "12px",
                              overflow: "auto",
                              marginTop: "8px"
                            }}>
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </BlockStack>
                    </div>
                  ))}
                  
                  <div style={{ 
                    padding: "16px", 
                    backgroundColor: "#f0f8ff", 
                    borderRadius: "8px",
                    border: "1px solid #2E7D32"
                  }}>
                    <Text as="h3" variant="headingSm">Summary</Text>
                    <Text as="p" variant="bodyMd">
                      {testResults.filter(r => r.success).length} out of {testResults.length} tests passed
                    </Text>
                    {testResults.every(r => r.success) && (
                      <Text as="p" variant="bodyMd" tone="success">
                        🎉 All tests passed! The BROpoints level system is working correctly.
                      </Text>
                    )}
                  </div>
                </BlockStack>
              </Card>
            )}
          </BlockStack>
        </Layout.Section>

        <Layout.Section secondary>
          <Card>
            <BlockStack gap="400">
              <Text as="h2" variant="headingMd">Test Information</Text>
              
              <Text as="h3" variant="headingSm">What gets tested:</Text>
              <List type="bullet">
                <List.Item>Level configuration (BRO, EhrenBRO, Bester BRO)</List.Item>
                <List.Item>Level calculation logic based on points</List.Item>
                <List.Item>Customer promotion when reaching thresholds</List.Item>
                <List.Item>Points multiplier application (1x, 1.25x, 1.5x)</List.Item>
              </List>
              
              <Text as="h3" variant="headingSm">Level Thresholds:</Text>
              <List type="bullet">
                <List.Item><strong>BRO:</strong> 0+ points (1x multiplier)</List.Item>
                <List.Item><strong>EhrenBRO:</strong> 1000+ points (1.25x multiplier)</List.Item>
                <List.Item><strong>Bester BRO:</strong> 5000+ points (1.5x multiplier)</List.Item>
              </List>
              
              <Text as="p" variant="bodyMd" tone="subdued">
                These tests use a test customer and don't affect real customer data.
              </Text>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
