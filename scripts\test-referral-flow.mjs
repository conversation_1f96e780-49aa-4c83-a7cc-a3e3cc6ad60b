import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Script de test pour vérifier le flux complet de parrainage
 */

async function testReferralFlow() {
  console.log('🧪 Test du flux complet de parrainage');
  console.log('=====================================\n');

  const shop = "test-shop.myshopify.com";
  const referrerShopifyId = "referrer-123";
  const referredShopifyId = "referred-456";

  try {
    // 1. Nettoyer les données de test existantes
    console.log('🧹 Nettoyage des données de test...');

    // Supprimer d'abord les pointsHistory (pas de contraintes)
    await prisma.pointsHistory.deleteMany({
      where: {
        OR: [
          { action: "referred_signup" },
          { action: "referral_validated" },
          { action: "referred_existing_member" },
          { action: "referrer_reward" }
        ]
      }
    });

    // Ensuite les referrals (qui référencent les customers)
    await prisma.referral.deleteMany({
      where: { shop }
    });

    // Enfin les customers
    await prisma.customer.deleteMany({
      where: { shop }
    });

    // 2. Créer les paramètres de base du shop
    console.log('⚙️ Configuration des paramètres de base...');
    await prisma.settings.upsert({
      where: { shop },
      update: {},
      create: {
        shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        pointsName: "Points",
        welcomeMessage: "Bienvenue dans notre programme de fidélité de test !"
      }
    });

    // 3. Créer les paramètres de parrainage
    console.log('⚙️ Configuration des paramètres de parrainage...');
    await prisma.referralSettings.upsert({
      where: { shop },
      update: {},
      create: {
        shop,
        active: true,
        referrerReward: JSON.stringify({ type: "points", amount: 100 }),
        referredReward: JSON.stringify({ type: "points", amount: 50 }),
        minimumPurchase: 25.0,
        expiryDays: 30,
        customMessage: "Test de parrainage"
      }
    });

    // 4. Créer le client parrain
    console.log('👤 Création du client parrain...');
    const referrerCustomer = await prisma.customer.create({
      data: {
        customerId: referrerShopifyId,
        shop,
        firstName: "John",
        lastName: "Referrer",
        email: "<EMAIL>",
        type: "member",
        points: 0
      }
    });
    console.log(`   ✅ Parrain créé: ${referrerCustomer.email} (ID interne: ${referrerCustomer.id})`);

    // 5. Générer un lien de parrainage manuellement
    console.log('🔗 Génération du lien de parrainage...');

    // Générer un code unique
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();

    // Créer le template de parrainage
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    const referralTemplate = await prisma.referral.create({
      data: {
        shop,
        referrerId: referrerCustomer.id,
        code,
        status: "active",
        expiresAt,
        referredId: null
      }
    });

    const linkResult = {
      success: true,
      referralUrl: `https://${shop}?ref=${code}`,
      code: code
    };
    
    if (!linkResult.success) {
      throw new Error(`Échec de la génération du lien: ${linkResult.error}`);
    }
    
    console.log(`   ✅ Lien généré: ${linkResult.referralUrl}`);
    console.log(`   📋 Code: ${linkResult.code}`);

    // 6. Simuler l'inscription d'un nouveau client avec le code de parrainage
    console.log('📝 Simulation de l\'inscription avec code de parrainage...');

    // D'abord créer le client référé
    const referredCustomer = await prisma.customer.create({
      data: {
        customerId: referredShopifyId,
        shop,
        firstName: "Jane",
        lastName: "Referred",
        email: "<EMAIL>",
        type: "member",
        points: 0
      }
    });
    console.log(`   ✅ Client référé créé: ${referredCustomer.email} (ID interne: ${referredCustomer.id})`);

    // Traiter le parrainage d'inscription manuellement
    console.log(`   🔍 Recherche du template avec le code: ${linkResult.code}`);

    // Trouver le template de parrainage
    const templateReferral = await prisma.referral.findFirst({
      where: {
        code: linkResult.code,
        shop,
        status: "active",
        expiresAt: { gt: new Date() },
        referredId: null
      },
      include: {
        referrer: true
      }
    });

    if (!templateReferral) {
      throw new Error("Template de parrainage non trouvé");
    }

    console.log(`   ✅ Template trouvé: ${templateReferral.id}`);

    // Créer une instance de parrainage avec un code unique
    const instanceCode = `${linkResult.code}-${referredCustomer.id.substring(0, 8)}`;
    const newReferral = await prisma.referral.create({
      data: {
        shop,
        referrerId: templateReferral.referrerId,
        referredId: referredCustomer.id,
        code: instanceCode,
        status: "pending",
        expiresAt: templateReferral.expiresAt,
        parentReferralId: templateReferral.id
      }
    });

    console.log(`   ✅ Instance de parrainage créée: ${newReferral.id}`);

    // Attribuer les points au filleul
    await prisma.pointsHistory.create({
      data: {
        ledgerId: referredCustomer.id,
        points: 50,
        action: "referred_signup",
        description: `Bonus d'inscription via parrainage`,
        metadata: JSON.stringify({
          referralCode: instanceCode,
          referrerCustomerId: templateReferral.referrerId,
          referralId: newReferral.id,
          isSignup: true
        })
      }
    });

    // Mettre à jour le solde du filleul
    await prisma.customer.update({
      where: { id: referredCustomer.id },
      data: {
        points: { increment: 50 }
      }
    });

    console.log(`   ✅ Parrainage d'inscription traité avec succès`);

    // 7. Vérifier les points attribués au filleul
    console.log('🎯 Vérification des points du filleul...');
    const updatedReferredCustomer = await prisma.customer.findUnique({
      where: { id: referredCustomer.id }
    });
    console.log(`   📊 Points du filleul: ${updatedReferredCustomer.points} (attendu: 50)`);

    // 8. Vérifier l'historique des points du filleul
    const referredHistory = await prisma.pointsHistory.findMany({
      where: { ledgerId: referredCustomer.id }
    });
    console.log(`   📜 Historique du filleul: ${referredHistory.length} entrée(s)`);
    referredHistory.forEach(entry => {
      console.log(`      - ${entry.action}: ${entry.points} points - ${entry.description}`);
    });

    // 9. Vérifier le statut du parrainage
    console.log('📋 Vérification du statut du parrainage...');
    const referralInstance = await prisma.referral.findFirst({
      where: {
        shop,
        referredId: referredCustomer.id,
        parentReferralId: { not: null }
      }
    });

    if (referralInstance) {
      console.log(`   ✅ Instance de parrainage trouvée: statut "${referralInstance.status}"`);
    } else {
      console.log(`   ❌ Aucune instance de parrainage trouvée`);
    }

    // 10. Simuler une première commande
    console.log('🛒 Simulation de la première commande...');
    const orderTotal = 50.0; // Au-dessus du minimum de 25€

    // Trouver le parrainage en attente
    const pendingReferral = await prisma.referral.findFirst({
      where: {
        shop,
        referredId: referredCustomer.id,
        status: "pending",
        expiresAt: { gt: new Date() },
        parentReferralId: { not: null }
      },
      include: {
        referrer: true
      }
    });

    if (!pendingReferral) {
      console.log(`   ❌ Aucun parrainage en attente trouvé`);
    } else {
      console.log(`   ✅ Parrainage en attente trouvé: ${pendingReferral.id}`);

      // Vérifier le montant minimum
      if (orderTotal >= 25.0) {
        // Valider le parrainage
        await prisma.referral.update({
          where: { id: pendingReferral.id },
          data: {
            status: "completed",
            completedAt: new Date()
          }
        });

        // Attribuer les points au parrain
        await prisma.pointsHistory.create({
          data: {
            ledgerId: pendingReferral.referrerId,
            points: 100,
            action: "referral_validated",
            description: `Parrainage validé - ${referredCustomer.email} a effectué un achat de ${orderTotal}€`,
            metadata: JSON.stringify({
              referralCode: pendingReferral.code,
              referredCustomerId: referredCustomer.customerId,
              referralId: pendingReferral.id,
              orderTotal,
              minimumPurchase: 25.0
            })
          }
        });

        // Mettre à jour le solde du parrain
        await prisma.customer.update({
          where: { id: pendingReferral.referrerId },
          data: {
            points: { increment: 100 }
          }
        });

        console.log(`   ✅ Parrainage validé avec succès`);
      } else {
        console.log(`   ⚠️ Montant insuffisant: ${orderTotal} < 25.0`);
      }
    }

    // 11. Vérifier les points finaux
    console.log('🏆 Vérification des points finaux...');

    const finalReferrerCustomer = await prisma.customer.findUnique({
      where: { id: referrerCustomer.id }
    });
    const finalReferredCustomer = await prisma.customer.findUnique({
      where: { id: referredCustomer.id }
    });

    console.log(`   📊 Points finaux du parrain: ${finalReferrerCustomer.points} (attendu: 100)`);
    console.log(`   📊 Points finaux du filleul: ${finalReferredCustomer.points} (attendu: 50)`);

    // 12. Vérifier l'historique complet
    console.log('📜 Historique complet des points...');

    const referrerHistory = await prisma.pointsHistory.findMany({
      where: { ledgerId: referrerCustomer.id }
    });
    console.log(`   Parrain (${referrerHistory.length} entrée(s)):`);
    referrerHistory.forEach(entry => {
      console.log(`      - ${entry.action}: ${entry.points} points - ${entry.description}`);
    });

    const finalReferredHistory = await prisma.pointsHistory.findMany({
      where: { ledgerId: referredCustomer.id }
    });
    console.log(`   Filleul (${finalReferredHistory.length} entrée(s)):`);
    finalReferredHistory.forEach(entry => {
      console.log(`      - ${entry.action}: ${entry.points} points - ${entry.description}`);
    });

    // 13. Vérifier le statut final du parrainage
    const finalReferral = await prisma.referral.findFirst({
      where: {
        shop,
        referredId: referredCustomer.id,
        parentReferralId: { not: null }
      }
    });
    
    if (finalReferral) {
      console.log(`   📋 Statut final du parrainage: "${finalReferral.status}"`);
      if (finalReferral.completedAt) {
        console.log(`   📅 Complété le: ${finalReferral.completedAt}`);
      }
    }

    console.log('\n✅ Test terminé avec succès !');

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le test
testReferralFlow();
