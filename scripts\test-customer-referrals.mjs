/**
 * Script de test pour vérifier les fonctionnalités de parrainage dans la page client
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestData() {
  console.log('🧪 Création de données de test pour les parrainages...');
  
  try {
    const shop = "test-shop.myshopify.com";
    
    // 1. Créer les paramètres du shop
    await prisma.settings.upsert({
      where: { shop: shop },
      update: {},
      create: {
        shop: shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        pointsName: "Points",
        welcomeMessage: "Bienvenue dans notre programme de fidélité de test !"
      }
    });

    // 2. Créer les paramètres de parrainage
    await prisma.referralSettings.upsert({
      where: { shop: shop },
      update: {},
      create: {
        shop: shop,
        active: true,
        referrerReward: JSON.stringify({ type: "points", amount: 100 }),
        referredReward: JSON.stringify({ type: "points", amount: 50 }),
        minimumPurchase: 25.0,
        expiryDays: 30,
        customMessage: "Parrainez vos amis et gagnez des points !"
      }
    });

    // 3. Créer un client parrain
    const referrer = await prisma.customer.create({
      data: {
        customerId: "test-referrer-123",
        shop: shop,
        firstName: "Alice",
        lastName: "Dupont",
        email: "<EMAIL>",
        type: "member",
        points: 250,
        totalSpent: 150.0,
        ordersCount: 3
      }
    });

    // 4. Créer un client filleul
    const referred = await prisma.customer.create({
      data: {
        customerId: "test-referred-456",
        shop: shop,
        firstName: "Bob",
        lastName: "Martin",
        email: "<EMAIL>",
        type: "guest",
        points: 50,
        totalSpent: 75.0,
        ordersCount: 1
      }
    });

    // 5. Créer des parrainages de test
    const activeReferral = await prisma.referral.create({
      data: {
        shop: shop,
        referrerId: referrer.id,
        code: "TEST_ACTIVE_REF",
        status: "pending",
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 jours
      }
    });

    const completedReferral = await prisma.referral.create({
      data: {
        shop: shop,
        referrerId: referrer.id,
        referredId: referred.id,
        code: "TEST_COMPLETED_REF",
        status: "completed",
        completedAt: new Date(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      }
    });

    // 6. Créer de l'historique de points
    await prisma.pointsHistory.createMany({
      data: [
        {
          ledgerId: referrer.id,
          action: "earn",
          points: 50,
          description: "Points gagnés pour une commande",
          metadata: JSON.stringify({ orderId: "order-123" })
        },
        {
          ledgerId: referrer.id,
          action: "referral_validated",
          points: 100,
          description: "Parrainage validé - <EMAIL> a effectué un achat",
          metadata: JSON.stringify({ referralCode: "TEST_COMPLETED_REF" })
        },
        {
          ledgerId: referred.id,
          action: "referred_signup",
          points: 50,
          description: "Bonus d'inscription via parrainage",
          metadata: JSON.stringify({ referralCode: "TEST_COMPLETED_REF" })
        }
      ]
    });

    console.log('✅ Données de test créées avec succès !');
    console.log(`   - Client parrain: ${referrer.firstName} ${referrer.lastName} (ID: ${referrer.id})`);
    console.log(`   - Client filleul: ${referred.firstName} ${referred.lastName} (ID: ${referred.id})`);
    console.log(`   - Parrainage actif: ${activeReferral.code}`);
    console.log(`   - Parrainage complété: ${completedReferral.code}`);
    
    return { referrer, referred, activeReferral, completedReferral };

  } catch (error) {
    console.error('❌ Erreur lors de la création des données de test:', error);
    throw error;
  }
}

async function testReferralFunctions() {
  console.log('\n🧪 Test des fonctions de parrainage...');
  
  try {
    const shop = "test-shop.myshopify.com";
    
    // Importer les fonctions de parrainage
    const { getCustomerReferrals, createReferralLink } = await import('../app/models/Referral.server.ts');
    
    // Test 1: Récupérer les parrainages d'un client
    const referrals = await getCustomerReferrals(shop, "test-referrer-123");
    console.log(`✅ Parrainages récupérés: ${referrals.length} parrainage(s)`);
    
    referrals.forEach(ref => {
      console.log(`   - Code: ${ref.code}, Statut: ${ref.status}, Points: ${ref.pointsEarned || 0}`);
    });

    // Test 2: Créer un nouveau lien de parrainage (devrait retourner l'existant)
    const linkResult = await createReferralLink(shop, "test-referrer-123");
    if (linkResult.success) {
      console.log(`✅ Lien de parrainage: ${linkResult.referralUrl}`);
      console.log(`   - Code: ${linkResult.code}`);
    } else {
      console.log(`❌ Erreur création lien: ${linkResult.error}`);
    }

  } catch (error) {
    console.error('❌ Erreur lors du test des fonctions:', error);
  }
}

async function cleanup() {
  console.log('\n🧹 Nettoyage des données de test...');
  
  try {
    // Supprimer dans l'ordre inverse des dépendances
    await prisma.pointsHistory.deleteMany({
      where: {
        OR: [
          { metadata: { contains: "TEST_" } },
          { metadata: { contains: "order-123" } }
        ]
      }
    });

    await prisma.referral.deleteMany({
      where: {
        code: { startsWith: "TEST_" }
      }
    });

    await prisma.customer.deleteMany({
      where: {
        customerId: { in: ["test-referrer-123", "test-referred-456"] }
      }
    });

    await prisma.referralSettings.deleteMany({
      where: {
        shop: "test-shop.myshopify.com"
      }
    });

    await prisma.settings.deleteMany({
      where: {
        shop: "test-shop.myshopify.com"
      }
    });

    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

async function main() {
  try {
    await cleanup(); // Nettoyer d'abord
    const testData = await createTestData();
    await testReferralFunctions();
    
    console.log('\n📋 Données de test créées. Vous pouvez maintenant tester la page client :');
    console.log(`   - URL: /app/customers/${testData.referrer.id}`);
    console.log(`   - Client: ${testData.referrer.firstName} ${testData.referrer.lastName}`);
    console.log(`   - Email: ${testData.referrer.email}`);
    
    console.log('\n🔍 Voulez-vous nettoyer les données de test ? (Ctrl+C pour garder)');
    
    // Attendre 10 secondes puis nettoyer automatiquement
    setTimeout(async () => {
      await cleanup();
      await prisma.$disconnect();
      process.exit(0);
    }, 10000);
    
  } catch (error) {
    console.error('❌ Erreur:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

main();
