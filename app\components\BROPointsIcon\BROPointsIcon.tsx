import React from 'react';

interface BROPointsIconProps {
  size?: number | string;
  color?: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Official BROpoints icon component
 * A stylized "B" with a star accent representing the BROpoints brand
 */
export function BROPointsIcon({ 
  size = 24, 
  color = "#2E7D32", 
  className = "",
  style = {}
}: BROPointsIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      {/* Main "B" shape */}
      <path
        d="M4 4h12c4.418 0 8 3.582 8 8 0 2.209-0.895 4.209-2.343 5.657C23.105 19.105 24 21.105 24 23.333c0 4.418-3.582 8-8 8H4V4z"
        fill={color}
      />
      
      {/* Inner cutouts for "B" */}
      <path
        d="M8 8v6h8c2.209 0 4-1.791 4-4s-1.791-4-4-4H8z"
        fill="white"
      />
      <path
        d="M8 18v6h8c2.209 0 4-1.791 4-4s-1.791-4-4-4H8z"
        fill="white"
      />
      
      {/* Star accent */}
      <path
        d="M26 6l1.5 3h3l-2.5 2 1 3-3-2-3 2 1-3-2.5-2h3L26 6z"
        fill="#4CAF50"
      />
    </svg>
  );
}

/**
 * Simplified BROpoints icon for small spaces
 */
export function BROPointsIconSimple({ 
  size = 16, 
  color = "#2E7D32", 
  className = "",
  style = {}
}: BROPointsIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      {/* Simplified "B" with star */}
      <circle cx="12" cy="12" r="10" fill={color} />
      <text
        x="12"
        y="16"
        textAnchor="middle"
        fill="white"
        fontSize="12"
        fontWeight="bold"
        fontFamily="Arial, sans-serif"
      >
        B
      </text>
      <path
        d="M18 8l1 2h2l-1.5 1.5 0.5 2-2-1-2 1 0.5-2L15 10h2l1-2z"
        fill="#4CAF50"
      />
    </svg>
  );
}

/**
 * BROpoints logo with text
 */
export function BROPointsLogo({ 
  size = 120, 
  color = "#2E7D32", 
  className = "",
  style = {}
}: BROPointsIconProps) {
  return (
    <div className={className} style={{ display: 'flex', alignItems: 'center', ...style }}>
      <BROPointsIcon size={size} color={color} />
      <div style={{ marginLeft: '12px' }}>
        <div style={{ 
          fontSize: `${typeof size === 'number' ? size * 0.4 : '24'}px`, 
          fontWeight: 'bold', 
          color: color,
          lineHeight: 1
        }}>
          BROpoints
        </div>
        <div style={{ 
          fontSize: `${typeof size === 'number' ? size * 0.2 : '12'}px`, 
          color: '#757575',
          lineHeight: 1
        }}>
          Loyalty Program
        </div>
      </div>
    </div>
  );
}

/**
 * BROpoints icon for use in CSS/HTML (returns SVG string)
 */
export function getBROPointsIconSVG(size: number = 24, color: string = "#2E7D32"): string {
  return `
    <svg width="${size}" height="${size}" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 4h12c4.418 0 8 3.582 8 8 0 2.209-0.895 4.209-2.343 5.657C23.105 19.105 24 21.105 24 23.333c0 4.418-3.582 8-8 8H4V4z" fill="${color}"/>
      <path d="M8 8v6h8c2.209 0 4-1.791 4-4s-1.791-4-4-4H8z" fill="white"/>
      <path d="M8 18v6h8c2.209 0 4-1.791 4-4s-1.791-4-4-4H8z" fill="white"/>
      <path d="M26 6l1.5 3h3l-2.5 2 1 3-3-2-3 2 1-3-2.5-2h3L26 6z" fill="#4CAF50"/>
    </svg>
  `.trim();
}

/**
 * BROpoints favicon (16x16 optimized)
 */
export function BROPointsFavicon() {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="16" height="16" rx="2" fill="#2E7D32"/>
      <text
        x="8"
        y="12"
        textAnchor="middle"
        fill="white"
        fontSize="10"
        fontWeight="bold"
        fontFamily="Arial, sans-serif"
      >
        B
      </text>
      <circle cx="13" cy="4" r="2" fill="#4CAF50"/>
    </svg>
  );
}
