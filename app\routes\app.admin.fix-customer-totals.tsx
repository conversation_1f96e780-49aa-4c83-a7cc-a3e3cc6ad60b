import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  But<PERSON>,
  Text,
  BlockStack,
  Banner,
  InlineStack,
  Spinner
} from "@shopify/polaris";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { recalculateAllCustomerTotals } from "app/models/Customer.server";
import { useTranslation } from "app/hooks/useTranslation";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "recalculate") {
    try {
      const result = await recalculateAllCustomerTotals(session.shop);
      
      if (result) {
        return json({
          success: true,
          message: `Recalcul terminé: ${result.successCount}/${result.total} clients mis à jour avec succès`,
          result
        });
      } else {
        return json({
          success: false,
          message: "Erreur lors du recalcul des totaux"
        });
      }
    } catch (error) {
      console.error("Error in recalculate action:", error);
      return json({
        success: false,
        message: "Erreur lors du recalcul des totaux"
      });
    }
  }

  return json({ success: false, message: "Action non reconnue" });
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return json({});
};

export default function FixCustomerTotals() {
  const navigation = useNavigation();
  const submit = useSubmit();
  const { t } = useTranslation();
  
  const isLoading = navigation.state === "submitting";

  const handleRecalculate = () => {
    const formData = new FormData();
    formData.append("action", "recalculate");
    submit(formData, { method: "post" });
  };

  return (
    <AdminLayout>
      <Page
        title="Correction des totaux clients"
        subtitle="Outil d'administration pour corriger les valeurs totalSpent et ordersCount"
        backAction={{ content: "Retour", url: "/app" }}
      >
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Banner tone="warning">
                  <Text as="p">
                    Cet outil recalcule les champs <strong>totalSpent</strong> et <strong>ordersCount</strong> 
                    de tous les clients en se basant sur leurs commandes réelles en base de données.
                  </Text>
                </Banner>

                <Text as="h2" variant="headingMd">
                  Problème détecté
                </Text>
                
                <Text as="p">
                  Les champs <code>totalSpent</code> et <code>ordersCount</code> peuvent être incorrects 
                  à cause d'une double mise à jour :
                </Text>
                
                <Text as="p">
                  • Les données Shopify (via webhooks customers/update) écrasent les valeurs<br/>
                  • Le service de points incrémente aussi ces valeurs<br/>
                  • Cela peut créer des incohérences
                </Text>

                <Text as="h2" variant="headingMd">
                  Solution
                </Text>
                
                <Text as="p">
                  Ce correctif :
                </Text>
                
                <Text as="p">
                  • Recalcule les totaux basés sur les commandes réelles<br/>
                  • Exclut les commandes annulées<br/>
                  • Met à jour la date de dernière commande<br/>
                  • Corrige la logique pour éviter les futurs doublons
                </Text>

                <InlineStack gap="300" align="start">
                  <Button
                    variant="primary"
                    onClick={handleRecalculate}
                    loading={isLoading}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <InlineStack gap="200" align="center">
                        <Spinner size="small" />
                        <Text as="span">Recalcul en cours...</Text>
                      </InlineStack>
                    ) : (
                      "Recalculer tous les totaux"
                    )}
                  </Button>
                </InlineStack>

                <Banner tone="info">
                  <Text as="p">
                    <strong>Note :</strong> Cette opération peut prendre quelques minutes selon 
                    le nombre de clients. Les logs détaillés sont visibles dans la console serveur.
                  </Text>
                </Banner>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    </AdminLayout>
  );
}
