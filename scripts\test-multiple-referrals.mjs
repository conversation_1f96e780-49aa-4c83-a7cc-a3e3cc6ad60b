import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupTestData() {
  console.log('🧹 Nettoyage des données de test...');
  
  // Supprimer les données de test
  await prisma.pointsHistory.deleteMany({
    where: {
      OR: [
        { description: { contains: 'Test Referral' } },
        { description: { contains: 'Bonus d\'inscription via parrainage' } },
        { description: { contains: 'Parrainage validé' } }
      ]
    }
  });
  
  await prisma.referral.deleteMany({
    where: {
      code: { startsWith: 'TEST' }
    }
  });
  
  await prisma.customer.deleteMany({
    where: {
      email: { contains: 'test-referral' }
    }
  });
  
  await prisma.settings.deleteMany({
    where: {
      shop: 'test-shop.myshopify.com'
    }
  });
  
  await prisma.referralSettings.deleteMany({
    where: {
      shop: 'test-shop.myshopify.com'
    }
  });
  
  console.log('✅ Nettoyage terminé');
}

async function createTestData() {
  console.log('🧪 Création des données de test...');
  
  const shop = 'test-shop.myshopify.com';
  
  // Créer les paramètres du shop
  await prisma.settings.create({
    data: {
      shop,
      pointsName: 'points',
      earningRate: 1.0,
      redemptionRate: 100.0
    }
  });
  
  // Créer les paramètres de parrainage
  await prisma.referralSettings.create({
    data: {
      shop,
      active: true,
      referrerReward: JSON.stringify({ type: 'points', amount: 100 }),
      referredReward: JSON.stringify({ type: 'points', amount: 50 }),
      minimumPurchase: 25,
      expiryDays: 30,
      customMessage: 'Test referral program'
    }
  });
  
  // Créer le client parrain
  const referrer = await prisma.customer.create({
    data: {
      customerId: 'referrer-123',
      shop,
      firstName: 'John',
      lastName: 'Referrer',
      email: '<EMAIL>',
      type: 'member',
      points: 0
    }
  });
  
  // Créer 3 clients qui vont être parrainés
  const referred1 = await prisma.customer.create({
    data: {
      customerId: 'referred-1',
      shop,
      firstName: 'Alice',
      lastName: 'Referred1',
      email: '<EMAIL>',
      type: 'guest',
      points: 0
    }
  });
  
  const referred2 = await prisma.customer.create({
    data: {
      customerId: 'referred-2',
      shop,
      firstName: 'Bob',
      lastName: 'Referred2',
      email: '<EMAIL>',
      type: 'guest',
      points: 0
    }
  });
  
  const referred3 = await prisma.customer.create({
    data: {
      customerId: 'referred-3',
      shop,
      firstName: 'Charlie',
      lastName: 'Referred3',
      email: '<EMAIL>',
      type: 'guest',
      points: 0
    }
  });
  
  console.log('✅ Données de test créées');
  return { shop, referrer, referred1, referred2, referred3 };
}

async function testMultipleReferrals() {
  try {
    await cleanupTestData();
    const { shop, referrer, referred1, referred2, referred3 } = await createTestData();
    
    console.log('\n🧪 Test du système de parrainages multiples...\n');
    
    // Étape 1: Créer un template de parrainage directement
    console.log('📝 Étape 1: Création du template de parrainage');

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    const referralTemplate = await prisma.referral.create({
      data: {
        shop,
        referrerId: referrer.id,
        code: 'TEST123',
        status: 'active',
        expiresAt,
        referredId: null
      }
    });

    console.log(`✅ Template créé: https://${shop}?ref=TEST123`);
    console.log(`   Code: TEST123`);
    
    // Étape 2: Simuler 3 personnes qui utilisent le même lien
    console.log('\n📝 Étape 2: Simulation de 3 inscriptions via le même lien');

    // Créer 3 instances de parrainage pour le même code
    const referral1 = await prisma.referral.create({
      data: {
        shop,
        referrerId: referrer.id,
        referredId: referred1.id,
        code: 'TEST123',
        status: 'pending',
        expiresAt,
        parentReferralId: referralTemplate.id
      }
    });
    console.log(`   Alice: ✅ Inscrite (Referral ID: ${referral1.id})`);

    const referral2 = await prisma.referral.create({
      data: {
        shop,
        referrerId: referrer.id,
        referredId: referred2.id,
        code: 'TEST123',
        status: 'pending',
        expiresAt,
        parentReferralId: referralTemplate.id
      }
    });
    console.log(`   Bob: ✅ Inscrit (Referral ID: ${referral2.id})`);

    const referral3 = await prisma.referral.create({
      data: {
        shop,
        referrerId: referrer.id,
        referredId: referred3.id,
        code: 'TEST123',
        status: 'pending',
        expiresAt,
        parentReferralId: referralTemplate.id
      }
    });
    console.log(`   Charlie: ✅ Inscrit (Referral ID: ${referral3.id})`);
    
    // Étape 3: Vérifier les parrainages créés
    console.log('\n📝 Étape 3: Vérification des parrainages créés');
    const referrals = await prisma.referral.findMany({
      where: { code: 'TEST123' },
      include: { referred: true }
    });

    console.log(`   Nombre total d'enregistrements: ${referrals.length}`);
    console.log(`   Template (status=active): ${referrals.filter(r => r.status === 'active').length}`);
    console.log(`   Instances (status=pending): ${referrals.filter(r => r.status === 'pending').length}`);

    referrals.forEach(ref => {
      if (ref.status === 'pending') {
        console.log(`   - ${ref.referred?.firstName} (${ref.status})`);
      }
    });
    
    // Étape 4: Simuler des validations de parrainages
    console.log('\n📝 Étape 4: Simulation de validations de parrainages');

    // Valider Alice (30€ > 25€ minimum)
    await prisma.referral.update({
      where: { id: referral1.id },
      data: { status: 'completed', completedAt: new Date() }
    });

    await prisma.pointsHistory.create({
      data: {
        ledgerId: referrer.id,
        points: 100,
        action: 'referral_validated',
        description: 'Parrainage validé - Alice a effectué un achat de 30€'
      }
    });

    await prisma.customer.update({
      where: { id: referrer.id },
      data: { points: { increment: 100 } }
    });
    console.log(`   Alice (30€): ✅ Parrainage validé`);

    // Valider Bob (50€ > 25€ minimum)
    await prisma.referral.update({
      where: { id: referral2.id },
      data: { status: 'completed', completedAt: new Date() }
    });

    await prisma.pointsHistory.create({
      data: {
        ledgerId: referrer.id,
        points: 100,
        action: 'referral_validated',
        description: 'Parrainage validé - Bob a effectué un achat de 50€'
      }
    });

    await prisma.customer.update({
      where: { id: referrer.id },
      data: { points: { increment: 100 } }
    });
    console.log(`   Bob (50€): ✅ Parrainage validé`);

    // Charlie ne valide pas (20€ < 25€ minimum)
    console.log(`   Charlie (20€): ❌ Montant minimum non atteint`);
    
    // Étape 5: Vérifier les points attribués
    console.log('\n📝 Étape 5: Vérification des points');
    const updatedReferrer = await prisma.customer.findUnique({
      where: { id: referrer.id }
    });
    
    const pointsHistory = await prisma.pointsHistory.findMany({
      where: { ledgerId: referrer.id },
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`   Points du parrain: ${updatedReferrer?.points} (attendu: 200)`);
    console.log(`   Historique des points: ${pointsHistory.length} entrées`);
    
    pointsHistory.forEach(entry => {
      console.log(`   - ${entry.action}: ${entry.points} points - ${entry.description}`);
    });
    
    // Étape 6: Test de récupération des parrainages
    console.log('\n📝 Étape 6: Test de récupération des parrainages');

    // Récupérer les instances de parrainage (avec parentReferralId)
    const customerReferrals = await prisma.referral.findMany({
      where: {
        referrerId: referrer.id,
        parentReferralId: { not: null }
      },
      include: { referred: true }
    });

    // Récupérer le template
    const template = await prisma.referral.findFirst({
      where: {
        referrerId: referrer.id,
        status: 'active',
        parentReferralId: null
      }
    });

    console.log(`   Parrainages effectifs: ${customerReferrals.length}`);
    console.log(`   Template actif: ${template ? 'Oui' : 'Non'}`);

    customerReferrals.forEach(ref => {
      console.log(`   - ${ref.referred?.email}: ${ref.status} (${ref.status === 'completed' ? '100' : '0'} points)`);
    });
    
    console.log('\n🎉 Test terminé avec succès !');
    
    // Résumé
    console.log('\n📊 Résumé:');
    console.log(`   ✅ Un lien peut parrainer plusieurs personnes`);
    console.log(`   ✅ Chaque parrainage est indépendant`);
    console.log(`   ✅ Les validations fonctionnent séparément`);
    console.log(`   ✅ Les points sont correctement attribués`);
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    console.log('\n🔍 Voulez-vous nettoyer les données de test ? (Ctrl+C pour garder)');
    
    // Attendre 3 secondes puis nettoyer
    await new Promise(resolve => setTimeout(resolve, 3000));
    await cleanupTestData();
    await prisma.$disconnect();
  }
}

testMultipleReferrals();
