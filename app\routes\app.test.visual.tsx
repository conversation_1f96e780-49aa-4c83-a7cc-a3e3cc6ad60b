import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Layout,
  Card,
  Button,
  BlockStack,
  Text,
  Banner,
  InlineStack,
  Grid,
} from "@shopify/polaris";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import { BROPointsIcon, BROPointsIconSimple, BROPointsLogo, BROPointsFavicon } from "app/components/BROPointsIcon/BROPointsIcon";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  return json({ 
    shop: session.shop
  });
};

export default function VisualTest() {
  const { shop } = useLoaderData<typeof loader>();

  return (
    <AdminLayout title="Visual Customization Test">
      <Layout>
        <Layout.Section>
          <BlockStack gap="500">
            <Banner tone="info">
              <Text as="p" variant="bodyMd">
                This page demonstrates all the visual customization features implemented for the BROpoints loyalty program.
                Use this to verify that all branding elements are working correctly.
              </Text>
            </Banner>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">BROpoints Icon Variations</Text>
                
                <Grid>
                  <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 3, lg: 3, xl: 3}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">Standard Icon</Text>
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                          <BROPointsIcon size={48} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">48px size</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                  
                  <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 3, lg: 3, xl: 3}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">Simple Icon</Text>
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                          <BROPointsIconSimple size={48} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">48px size</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                  
                  <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 3, lg: 3, xl: 3}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">Logo with Text</Text>
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                          <BROPointsLogo size={60} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">60px size</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                  
                  <Grid.Cell columnSpan={{xs: 6, sm: 3, md: 3, lg: 3, xl: 3}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">Favicon</Text>
                        <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                          <BROPointsFavicon />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">16px size</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                </Grid>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Brand Color Presets</Text>
                
                <Grid>
                  <Grid.Cell columnSpan={{xs: 6, sm: 4, md: 4, lg: 4, xl: 4}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">BROpoints Classic</Text>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#2E7D32', borderRadius: '4px' }} />
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#4CAF50', borderRadius: '4px' }} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">Primary: #2E7D32, Secondary: #4CAF50</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                  
                  <Grid.Cell columnSpan={{xs: 6, sm: 4, md: 4, lg: 4, xl: 4}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">BROpoints Dark</Text>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#1B5E20', borderRadius: '4px' }} />
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#2E7D32', borderRadius: '4px' }} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">Primary: #1B5E20, Secondary: #2E7D32</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                  
                  <Grid.Cell columnSpan={{xs: 6, sm: 4, md: 4, lg: 4, xl: 4}}>
                    <Card>
                      <BlockStack gap="200">
                        <Text as="h3" variant="headingSm">BROpoints Gold</Text>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#2E7D32', borderRadius: '4px' }} />
                          <div style={{ width: '40px', height: '40px', backgroundColor: '#FFD700', borderRadius: '4px' }} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued">Primary: #2E7D32, Secondary: #FFD700</Text>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                </Grid>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">CSS Integration Test</Text>
                
                <div style={{ 
                  padding: '20px', 
                  backgroundColor: '#f8f9fa', 
                  borderRadius: '8px',
                  border: '1px solid #e0e0e0'
                }}>
                  <Text as="h3" variant="headingSm">CSS Variables Available:</Text>
                  <div style={{ marginTop: '12px', fontFamily: 'monospace', fontSize: '12px' }}>
                    <div>--loyalty-primary: #2E7D32</div>
                    <div>--loyalty-secondary: #4CAF50</div>
                    <div>--bropoints-icon: [SVG data URL]</div>
                    <div>--bropoints-icon-white: [SVG data URL]</div>
                  </div>
                </div>

                <div style={{ 
                  padding: '20px', 
                  backgroundColor: 'var(--loyalty-primary, #2E7D32)', 
                  color: 'white',
                  borderRadius: '8px'
                }}>
                  <Text as="p" variant="bodyMd" tone="inherit">
                    This box uses CSS variable --loyalty-primary for background color
                  </Text>
                </div>

                <div className="bropoints-icon" style={{ margin: '20px auto', display: 'block' }}>
                  {/* This should show the BROpoints icon via CSS */}
                </div>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Widget Integration Status</Text>
                
                <InlineStack gap="300">
                  <div style={{ 
                    padding: '12px 16px', 
                    backgroundColor: '#e8f5e8', 
                    borderRadius: '6px',
                    border: '1px solid #4CAF50'
                  }}>
                    <Text as="p" variant="bodyMd" fontWeight="medium">
                      ✅ Main Widget (custom_rewards_app.liquid)
                    </Text>
                  </div>
                  
                  <div style={{ 
                    padding: '12px 16px', 
                    backgroundColor: '#e8f5e8', 
                    borderRadius: '6px',
                    border: '1px solid #4CAF50'
                  }}>
                    <Text as="p" variant="bodyMd" fontWeight="medium">
                      ✅ Product Widget (loyalty_product_widget.liquid)
                    </Text>
                  </div>
                  
                  <div style={{ 
                    padding: '12px 16px', 
                    backgroundColor: '#e8f5e8', 
                    borderRadius: '6px',
                    border: '1px solid #4CAF50'
                  }}>
                    <Text as="p" variant="bodyMd" fontWeight="medium">
                      ✅ Account Widget (loyalty_account_widget.liquid)
                    </Text>
                  </div>
                  
                  <div style={{ 
                    padding: '12px 16px', 
                    backgroundColor: '#e8f5e8', 
                    borderRadius: '6px',
                    border: '1px solid #4CAF50'
                  }}>
                    <Text as="p" variant="bodyMd" fontWeight="medium">
                      ✅ Thank You Widget (loyalty_thankyou_widget.liquid)
                    </Text>
                  </div>
                </InlineStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">Test Actions</Text>
                
                <InlineStack gap="300">
                  <Button 
                    variant="primary"
                    url="/app/settings/widget"
                  >
                    Test Widget Settings
                  </Button>
                  
                  <Button 
                    url="/app/test/levels"
                  >
                    Test Level System
                  </Button>
                  
                  <Button 
                    url="/app/program/vip"
                  >
                    View BROpoints Levels
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </AdminLayout>
  );
}
