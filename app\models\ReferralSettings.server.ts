import prisma from "../db.server";

export interface ReferralReward {
  type: "points" | "discount" | "fixed";
  amount: number;
}

export interface ReferralSettingsData {
  active: boolean;
  referrerReward: ReferralReward;
  referredReward: ReferralReward;
  minimumPurchase: number;
  expiryDays: number;
  customMessage: string;
}

/**
 * Récupérer les paramètres de parrainage pour un shop
 */
export async function getReferralSettings(shop: string): Promise<ReferralSettingsData | null> {
  try {
    const settings = await prisma.referralSettings.findUnique({
      where: { shop }
    });

    if (!settings) {
      return null;
    }

    return {
      active: settings.active,
      referrerReward: JSON.parse(settings.referrerReward),
      referredReward: JSON.parse(settings.referredReward),
      minimumPurchase: settings.minimumPurchase,
      expiryDays: settings.expiryDays,
      customMessage: settings.customMessage || ""
    };
  } catch (error) {
    console.error("Error fetching referral settings:", error);
    return null;
  }
}

/**
 * C<PERSON>er ou mettre à jour les paramètres de parrainage
 */
export async function upsertReferralSettings(
  shop: string, 
  data: ReferralSettingsData
): Promise<ReferralSettingsData | null> {
  try {
    const settings = await prisma.referralSettings.upsert({
      where: { shop },
      update: {
        active: data.active,
        referrerReward: JSON.stringify(data.referrerReward),
        referredReward: JSON.stringify(data.referredReward),
        minimumPurchase: data.minimumPurchase,
        expiryDays: data.expiryDays,
        customMessage: data.customMessage
      },
      create: {
        shop,
        active: data.active,
        referrerReward: JSON.stringify(data.referrerReward),
        referredReward: JSON.stringify(data.referredReward),
        minimumPurchase: data.minimumPurchase,
        expiryDays: data.expiryDays,
        customMessage: data.customMessage
      }
    });

    return {
      active: settings.active,
      referrerReward: JSON.parse(settings.referrerReward),
      referredReward: JSON.parse(settings.referredReward),
      minimumPurchase: settings.minimumPurchase,
      expiryDays: settings.expiryDays,
      customMessage: settings.customMessage || ""
    };
  } catch (error) {
    console.error("Error upserting referral settings:", error);
    return null;
  }
}

/**
 * Obtenir les paramètres par défaut
 */
export function getDefaultReferralSettings(): ReferralSettingsData {
  return {
    active: false,
    referrerReward: {
      type: "points",
      amount: 100
    },
    referredReward: {
      type: "points", 
      amount: 50
    },
    minimumPurchase: 0,
    expiryDays: 30,
    customMessage: "Parrainez vos amis et gagnez des récompenses !"
  };
}
