# Guide d'Intégration - Custom Loyalty Rewards App

## 🎯 Ouverture du Hub sans Bouton Flottant

Cette application offre plusieurs méthodes pour intégrer le widget de fidélité dans votre thème Shopify sans utiliser le bouton flottant par défaut.

---

## 📋 Configuration de Base

### 1. Masquer le Bouton Flottant

Dans l'éditeur de thème Shopify :
1. Allez dans **Personnaliser** > **Extensions d'application**
2. Trouvez le bloc **Custom Rewards App**
3. Activez l'option **"Masquer le bouton flottant"**

### 2. API JavaScript Disponible

Une fois le widget chargé, l'API suivante est disponible globalement :

```javascript
// Ouvrir le hub de fidélité
window.loyaltyWidget.open();

// Fermer le hub de fidélité
window.loyaltyWidget.close();

// Basculer l'état (ouvrir/fermer)
window.loyaltyWidget.toggle();
```

---

## 🧩 Blocs Liquid Spécialisés

### Blocs Disponibles

L'application fournit 4 blocs Liquid spécialisés pour différentes pages :

1. **`loyalty_product_widget`** - Page produit
2. **`loyalty_checkout_widget`** - Page checkout/panier
3. **`loyalty_thankyou_widget`** - Page Thank You
4. **`loyalty_account_widget`** - Espace client

### Installation des Blocs

1. Dans l'éditeur de thème Shopify
2. Allez sur la page concernée (produit, checkout, etc.)
3. Cliquez sur **"Ajouter une section"**
4. Trouvez **"Extensions d'application"**
5. Sélectionnez le bloc de fidélité approprié

### Fonctionnement

Chaque bloc :
- **Affiche** les informations de fidélité pertinentes
- **S'adapte** au contexte (connecté/non connecté)
- **Ouvre le widget** au clic
- **Se met à jour** automatiquement

---

## 🔧 Méthodes d'Intégration

### Méthode 1 : Blocs Liquid (Recommandé)

**Page Produit :**
1. Éditeur de thème > Page produit
2. Ajouter section > Extensions d'application
3. Sélectionner **"Widget Fidélité Produit"**

**Page Checkout :**
1. Éditeur de thème > Checkout
2. Ajouter section > Extensions d'application
3. Sélectionner **"Widget Fidélité Checkout"**

**Page Thank You :**
1. Éditeur de thème > Thank You
2. Ajouter section > Extensions d'application
3. Sélectionner **"Widget Fidélité Thank You"**

**Espace Client :**
1. Éditeur de thème > Compte client
2. Ajouter section > Extensions d'application
3. Sélectionner **"Widget Fidélité Compte"**

### Méthode 2 : Lien dans le Menu Principal

Ajoutez ce code dans votre fichier de menu (généralement `header.liquid` ou `navigation.liquid`) :

```liquid
<!-- Lien Club BROpoints dans le menu -->
<a href="#" onclick="window.loyaltyWidget.open(); return false;" class="loyalty-menu-link">
  <span>Club BROpoints</span>
  {% if customer %}
    <span class="loyalty-points-badge" id="header-points-display">0 pts</span>
  {% endif %}
</a>
```

### Méthode 3 : Bouton Personnalisé

Créez un bouton personnalisé n'importe où dans votre thème :

```liquid
<!-- Bouton personnalisé -->
<button type="button" onclick="window.loyaltyWidget.open()" class="btn-loyalty">
  🏆 Mes Récompenses
</button>
```

---

## 🎨 Styles CSS Recommandés

Ajoutez ces styles à votre fichier CSS principal :

```css
/* Styles pour les liens de fidélité */
.loyalty-menu-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: inherit;
}

.loyalty-points-badge {
  background: #2E7D32;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.btn-loyalty {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.btn-loyalty:hover {
  transform: translateY(-2px);
}

.product-loyalty-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  border-left: 4px solid #2E7D32;
}

.loyalty-earn-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.loyalty-icon {
  font-size: 18px;
}
```

---

## 📱 Responsive et Mobile

Le widget s'adapte automatiquement aux écrans mobiles. Pour une meilleure expérience :

```css
/* Styles responsive */
@media (max-width: 768px) {
  .loyalty-menu-link {
    flex-direction: column;
    gap: 4px;
  }
  
  .loyalty-points-badge {
    font-size: 10px;
    padding: 1px 6px;
  }
  
  .btn-loyalty {
    width: 100%;
    padding: 16px;
  }
}
```

---

## 🔄 Synchronisation des Points

Pour afficher le solde de points en temps réel dans votre thème :

```javascript
// Fonction pour mettre à jour l'affichage des points
function updatePointsDisplay() {
  if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
    const points = window.loyaltyWidget.customerData.points || 0;
    
    // Mettre à jour tous les éléments d'affichage des points
    const pointsElements = document.querySelectorAll('#header-points-display, #account-points-display');
    pointsElements.forEach(element => {
      element.textContent = `${points} pts`;
    });
  }
}

// Mettre à jour l'affichage quand le widget est chargé
document.addEventListener('DOMContentLoaded', function() {
  // Attendre que le widget soit initialisé
  setTimeout(updatePointsDisplay, 2000);
});

// Écouter les changements de points
window.addEventListener('loyaltyPointsUpdated', updatePointsDisplay);
```

---

## ✅ Vérification de l'Intégration

### Page de Test Incluse

Une page de test complète est disponible : `test-integration.html`

Cette page permet de :
- Vérifier que l'API est disponible
- Tester toutes les méthodes JavaScript
- Voir des exemples d'intégration
- Déboguer les problèmes

### Tests Manuels

1. **Console du navigateur** : Vérifiez que `window.loyaltyWidget` est défini
2. **Test d'ouverture** : Cliquez sur vos liens personnalisés
3. **Responsive** : Testez sur mobile et desktop
4. **Points** : Vérifiez que les points s'affichent correctement

```javascript
// Test dans la console du navigateur
console.log('Widget disponible:', !!window.loyaltyWidget);
console.log('Données client:', window.loyaltyWidget?.customerData);
console.log('Méthodes disponibles:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.loyaltyWidget)));
```

### Snippets Liquid Prêts à l'Emploi

Trois snippets sont disponibles dans le dossier `snippets/` :

1. **`loyalty-menu-link.liquid`** - Lien pour le menu de navigation
2. **`loyalty-product-points.liquid`** - Affichage des points sur les pages produit
3. **`loyalty-account-section.liquid`** - Section complète pour l'espace client

Consultez `snippets/README.md` pour les instructions détaillées.

---

## 🚨 Dépannage

### Problèmes Courants

1. **`window.loyaltyWidget` non défini**
   - Vérifiez que le bloc Custom Rewards App est ajouté au thème
   - Attendez le chargement complet de la page

2. **Le widget ne s'ouvre pas**
   - Vérifiez la console pour les erreurs JavaScript
   - Assurez-vous que l'API Proxy est configurée

3. **Points non affichés**
   - Vérifiez que le client est connecté
   - Contrôlez la configuration du programme de fidélité

### Support

Pour toute assistance technique, contactez l'équipe de développement avec :
- URL de la boutique
- Description du problème
- Capture d'écran de la console du navigateur
