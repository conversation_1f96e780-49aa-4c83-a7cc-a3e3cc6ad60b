import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigate } from "@remix-run/react";
import { authenticate } from "../shopify.server";
import { getSiteSettings, upsertSiteSettings, getDefaultSiteSettings, type SiteSettingsData } from "../models/SiteSettings.server";
import {
  Card,
  Layout,
  Page,
  BlockStack,
  InlineStack,
  Text,
  TextField,
  Select,
  Checkbox,
  Button,
  Grid,
  Box,
  Divider,
  Toast,
  Frame,
  Modal,
  FormLayout,
} from "@shopify/polaris";
import { AdminLayout } from "../components/Layout/AdminLayout";
import { useState, useCallback } from "react";
import { useTranslation } from "../hooks/useTranslation";

import { WidgetSimulator } from "../components/WidgetSimulatorNew2";
import { ColorPicker } from "../components/ColorPicker";
import { BROPointsIconSimple } from "../components/BROPointsIcon/BROPointsIcon";

interface LoaderData {
  settings: SiteSettingsData | null;
  customerData: {
    name: string;
    email: string;
    points: number;
    orders: number;
    initials: string;
  };
  customPresets: Array<{
    id: string;
    name: string;
    description: string;
    primary: string;
    secondary: string;
    text: string;
  }>;
}

// BROpoints brand color presets
const BROPOINTS_COLOR_PRESETS = [
  {
    name: "BROpoints Classic",
    description: "Official BROpoints brand colors",
    primary: "#2E7D32",
    secondary: "#4CAF50",
    text: "#FFFFFF"
  },
  {
    name: "BROpoints Dark",
    description: "Dark theme with green accents",
    primary: "#1B5E20",
    secondary: "#2E7D32",
    text: "#FFFFFF"
  },
  {
    name: "BROpoints Light",
    description: "Light theme with subtle greens",
    primary: "#4CAF50",
    secondary: "#81C784",
    text: "#FFFFFF"
  },
  {
    name: "BROpoints Gold",
    description: "Premium gold and green combination",
    primary: "#2E7D32",
    secondary: "#FFD700",
    text: "#FFFFFF"
  },
  {
    name: "BROpoints Blue",
    description: "Professional blue variant",
    primary: "#1976D2",
    secondary: "#42A5F5",
    text: "#FFFFFF"
  }
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const settings = await getSiteSettings(shop);

    // Données client par défaut pour la simulation
    const customerData = {
      name: "Marie Dupont",
      email: "<EMAIL>",
      points: 1250,
      orders: 8,
      initials: "MD"
    };

    // Parse custom brand presets from settings
    let customPresets = [];
    try {
      customPresets = settings?.customBrandPresets ? JSON.parse(settings.customBrandPresets) : [];
    } catch (e) {
      console.warn("Error parsing custom brand presets:", e);
      customPresets = [];
    }

    return json({ settings, customerData, customPresets });
  } catch (error) {
    console.error("Error loading widget settings:", error);
    return json({ settings: getDefaultSiteSettings(shop), customerData: null, customPresets: [] });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  try {
    const formData = await request.formData();
    const action = formData.get("_action") as string;

    if (action === "saveCustomPreset") {
      // Handle custom preset saving
      const presetName = formData.get("presetName") as string;
      const primary = formData.get("primary") as string;
      const secondary = formData.get("secondary") as string;
      const text = formData.get("text") as string;

      if (!presetName || !primary || !secondary || !text) {
        return json({ success: false, message: "All fields are required" });
      }

      // Get current settings to access existing custom presets
      const currentSettings = await getSiteSettings(shop);
      let customPresets = [];
      try {
        customPresets = currentSettings?.customBrandPresets ? JSON.parse(currentSettings.customBrandPresets) : [];
      } catch (e) {
        customPresets = [];
      }

      // Add new preset
      const newPreset = {
        id: Date.now().toString(),
        name: presetName,
        description: `Custom preset: ${presetName}`,
        primary,
        secondary,
        text
      };

      customPresets.push(newPreset);

      // Save updated presets
      await upsertSiteSettings(shop, {
        customBrandPresets: JSON.stringify(customPresets)
      });

      return json({ success: true, message: "Custom preset saved successfully" });
    }

    if (action === "deleteCustomPreset") {
      const presetId = formData.get("presetId") as string;

      if (!presetId) {
        return json({ success: false, message: "Preset ID is required" });
      }

      // Get current settings
      const currentSettings = await getSiteSettings(shop);
      let customPresets = [];
      try {
        customPresets = currentSettings?.customBrandPresets ? JSON.parse(currentSettings.customBrandPresets) : [];
      } catch (e) {
        customPresets = [];
      }

      // Remove preset
      customPresets = customPresets.filter((preset: any) => preset.id !== presetId);

      // Save updated presets
      await upsertSiteSettings(shop, {
        customBrandPresets: JSON.stringify(customPresets)
      });

      return json({ success: true, message: "Custom preset deleted successfully" });
    }

    // Default widget settings save
    const rawData = Object.fromEntries(formData);

    // Convertir les valeurs booléennes
    const settingsData: any = { ...rawData };
    const booleanFields = ['widgetShadow', 'widgetAnimation', 'showPointsOnButton', 'widgetEnabled'];
    booleanFields.forEach(field => {
      if (rawData[field] !== undefined) {
        settingsData[field] = rawData[field] === 'true';
      }
    });

    await upsertSiteSettings(shop, settingsData);

    return json({ success: true, message: "Widget settings saved successfully" });
  } catch (error) {
    console.error("Error saving widget settings:", error);
    return json({ error: "Error saving settings" }, { status: 500 });
  }
};

export default function WidgetSettings() {
  const { settings, customerData, customPresets } = useLoaderData<typeof loader>() as LoaderData;
  const submit = useSubmit();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // États pour les paramètres du widget
  const [widgetColor, setWidgetColor] = useState(settings?.widgetColor || '#2E7D32');
  const [widgetSecondaryColor, setWidgetSecondaryColor] = useState(settings?.widgetSecondaryColor || '#4CAF50');
  const [widgetTextColor, setWidgetTextColor] = useState(settings?.widgetTextColor || '#FFFFFF');
  const [widgetPosition, setWidgetPosition] = useState(settings?.widgetPosition || 'bottom-right');
  const [widgetSize, setWidgetSize] = useState(settings?.widgetSize || 'medium');
  const [widgetBorderRadius, setWidgetBorderRadius] = useState(settings?.widgetBorderRadius || 'rounded');
  const [widgetShadow, setWidgetShadow] = useState(settings?.widgetShadow !== false);
  const [widgetAnimation, setWidgetAnimation] = useState(settings?.widgetAnimation !== false);
  const [showPointsOnButton, setShowPointsOnButton] = useState(settings?.showPointsOnButton !== false);
  const [pointsName, setPointsName] = useState(settings?.pointsName || 'Points');
  const [welcomeMessage, setWelcomeMessage] = useState(settings?.welcomeMessage || '');
  const [customCSS, setCustomCSS] = useState(settings?.customCSS || '');

  // État pour les toasts
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastError, setToastError] = useState(false);

  // État pour les presets personnalisés
  const [showPresetModal, setShowPresetModal] = useState(false);
  const [presetName, setPresetName] = useState('');

  const handleSubmit = useCallback(() => {
    const formData = new FormData();
    formData.append('widgetColor', widgetColor);
    formData.append('widgetSecondaryColor', widgetSecondaryColor);
    formData.append('widgetTextColor', widgetTextColor);
    formData.append('widgetPosition', widgetPosition);
    formData.append('widgetSize', widgetSize);
    formData.append('widgetBorderRadius', widgetBorderRadius);
    formData.append('widgetShadow', widgetShadow.toString());
    formData.append('widgetAnimation', widgetAnimation.toString());
    formData.append('showPointsOnButton', showPointsOnButton.toString());
    formData.append('pointsName', pointsName);
    formData.append('welcomeMessage', welcomeMessage);
    formData.append('customCSS', customCSS);

    submit(formData, { method: 'post' });

    setToastMessage(t('admin.settings.saveSuccess'));
    setToastError(false);
    setToastActive(true);
  }, [
    widgetColor, widgetSecondaryColor, widgetTextColor, widgetPosition,
    widgetSize, widgetBorderRadius, widgetShadow, widgetAnimation,
    showPointsOnButton, pointsName, welcomeMessage, customCSS, submit
  ]);

  const toggleToastActive = useCallback(() => setToastActive((active) => !active), []);

  const handleSaveAsPreset = useCallback(() => {
    if (!presetName.trim()) return;

    const formData = new FormData();
    formData.append('_action', 'saveCustomPreset');
    formData.append('presetName', presetName);
    formData.append('primary', widgetColor);
    formData.append('secondary', widgetSecondaryColor);
    formData.append('text', widgetTextColor);

    submit(formData, { method: 'post' });
    setShowPresetModal(false);
    setPresetName('');

    setToastMessage(t('admin.widget.brandPresets.presetSaved'));
    setToastError(false);
    setToastActive(true);
  }, [presetName, widgetColor, widgetSecondaryColor, widgetTextColor, submit, t]);

  const handleDeleteCustomPreset = useCallback((presetId: string) => {
    const formData = new FormData();
    formData.append('_action', 'deleteCustomPreset');
    formData.append('presetId', presetId);

    submit(formData, { method: 'post' });

    setToastMessage(t('admin.widget.brandPresets.presetDeleted'));
    setToastError(false);
    setToastActive(true);
  }, [submit, t]);

  const toastMarkup = toastActive ? (
    <Toast
      content={toastMessage}
      onDismiss={toggleToastActive}
      error={toastError}
    />
  ) : null;

  const positionOptions = [
    { label: t('admin.widget.position.bottomRight'), value: 'bottom-right' },
    { label: t('admin.widget.position.bottomLeft'), value: 'bottom-left' },
    { label: t('admin.widget.position.topRight'), value: 'top-right' },
    { label: t('admin.widget.position.topLeft'), value: 'top-left' },
  ];

  const sizeOptions = [
    { label: t('admin.widget.size.small'), value: 'small' },
    { label: t('admin.widget.size.medium'), value: 'medium' },
    { label: t('admin.widget.size.large'), value: 'large' },
  ];

  const borderRadiusOptions = [
    { label: t('admin.widget.borders.square'), value: 'square' },
    { label: t('admin.widget.borders.rounded'), value: 'rounded' },
    { label: t('admin.widget.borders.pill'), value: 'pill' },
  ];

  return (
    <Frame>
      <AdminLayout title={t('admin.widget.title')}>
        <Page fullWidth>
          <Layout>
            <Layout.Section variant="oneThird">
              <BlockStack gap="500">
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t('admin.widget.colors')}
                  </Text>

                  <ColorPicker
                    label={t('admin.widget.primaryColor')}
                    value={widgetColor}
                    onChange={setWidgetColor}
                  />

                  <ColorPicker
                    label={t('admin.widget.secondaryColor')}
                    value={widgetSecondaryColor}
                    onChange={setWidgetSecondaryColor}
                  />

                  <ColorPicker
                    label={t('admin.widget.textColor')}
                    value={widgetTextColor}
                    onChange={setWidgetTextColor}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <BROPointsIconSimple size={20} />
                      <Text as="h2" variant="headingMd">
                        {t('admin.widget.brandPresets.title')}
                      </Text>
                    </div>
                    <Button
                      size="slim"
                      onClick={() => setShowPresetModal(true)}
                    >
                      {t('admin.widget.brandPresets.saveAsPreset')}
                    </Button>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    {t('admin.widget.brandPresets.description')}
                  </Text>

                  <div style={{ display: 'grid', gap: '12px' }}>
                    {BROPOINTS_COLOR_PRESETS.map((preset, index) => {
                      const presetKey = preset.name.toLowerCase().replace(/\s+/g, '');
                      const translatedName = t(`admin.widget.brandPresets.${presetKey}.name`) || preset.name;
                      const translatedDescription = t(`admin.widget.brandPresets.${presetKey}.description`) || preset.description;

                      return (
                        <div
                          key={index}
                          style={{
                            padding: '12px',
                            border: '1px solid #e0e0e0',
                            borderRadius: '8px',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            backgroundColor: '#fafafa'
                          }}
                          onClick={() => {
                            setWidgetColor(preset.primary);
                            setWidgetSecondaryColor(preset.secondary);
                            setWidgetTextColor(preset.text);
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f0f0f0';
                            e.currentTarget.style.borderColor = preset.primary;
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#fafafa';
                            e.currentTarget.style.borderColor = '#e0e0e0';
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                            <div style={{ display: 'flex', gap: '4px' }}>
                              <div
                                style={{
                                  width: '20px',
                                  height: '20px',
                                  borderRadius: '4px',
                                  backgroundColor: preset.primary,
                                  border: '1px solid #ddd'
                                }}
                              />
                              <div
                                style={{
                                  width: '20px',
                                  height: '20px',
                                  borderRadius: '4px',
                                  backgroundColor: preset.secondary,
                                  border: '1px solid #ddd'
                                }}
                              />
                            </div>
                            <div>
                              <Text as="p" variant="bodyMd" fontWeight="medium">
                                {translatedName}
                              </Text>
                              <Text as="p" variant="bodySm" tone="subdued">
                                {translatedDescription}
                              </Text>
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {/* Custom Presets */}
                    {customPresets.length > 0 && (
                      <>
                        <div style={{ marginTop: '16px', marginBottom: '8px' }}>
                          <Text as="h3" variant="headingSm">
                            {t('admin.widget.brandPresets.customPresets')}
                          </Text>
                        </div>
                        {customPresets.map((preset: any) => (
                          <div
                            key={preset.id}
                            style={{
                              padding: '12px',
                              border: '1px solid #e0e0e0',
                              borderRadius: '8px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              backgroundColor: '#fafafa'
                            }}
                            onClick={() => {
                              setWidgetColor(preset.primary);
                              setWidgetSecondaryColor(preset.secondary);
                              setWidgetTextColor(preset.text);
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#f0f0f0';
                              e.currentTarget.style.borderColor = preset.primary;
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#fafafa';
                              e.currentTarget.style.borderColor = '#e0e0e0';
                            }}
                          >
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                <div style={{ display: 'flex', gap: '4px' }}>
                                  <div
                                    style={{
                                      width: '20px',
                                      height: '20px',
                                      borderRadius: '4px',
                                      backgroundColor: preset.primary,
                                      border: '1px solid #ddd'
                                    }}
                                  />
                                  <div
                                    style={{
                                      width: '20px',
                                      height: '20px',
                                      borderRadius: '4px',
                                      backgroundColor: preset.secondary,
                                      border: '1px solid #ddd'
                                    }}
                                  />
                                </div>
                                <div>
                                  <Text as="p" variant="bodyMd" fontWeight="medium">
                                    {preset.name}
                                  </Text>
                                  <Text as="p" variant="bodySm" tone="subdued">
                                    {preset.description}
                                  </Text>
                                </div>
                              </div>
                              <Button
                                size="slim"
                                tone="critical"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteCustomPreset(preset.id);
                                }}
                              >
                                {t('common.delete')}
                              </Button>
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t('admin.widget.appearance')}
                  </Text>

                  <Select
                    label={t('admin.widget.position.label')}
                    options={[
                      { label: t('admin.widget.position.bottomRight'), value: 'bottom-right' },
                      { label: t('admin.widget.position.bottomLeft'), value: 'bottom-left' },
                      { label: t('admin.widget.position.topRight'), value: 'top-right' },
                      { label: t('admin.widget.position.topLeft'), value: 'top-left' },
                    ]}
                    value={widgetPosition}
                    onChange={setWidgetPosition}
                  />

                  <Select
                    label={t('admin.widget.size.label')}
                    options={[
                      { label: t('admin.widget.size.small'), value: 'small' },
                      { label: t('admin.widget.size.medium'), value: 'medium' },
                      { label: t('admin.widget.size.large'), value: 'large' },
                    ]}
                    value={widgetSize}
                    onChange={setWidgetSize}
                  />

                  <Select
                    label={t('admin.widget.borders.label')}
                    options={[
                      { label: t('admin.widget.borders.square'), value: 'square' },
                      { label: t('admin.widget.borders.rounded'), value: 'rounded' },
                      { label: t('admin.widget.borders.pill'), value: 'pill' },
                    ]}
                    value={widgetBorderRadius}
                    onChange={setWidgetBorderRadius}
                  />

                  <Checkbox
                    label={t('admin.widget.shadow')}
                    checked={widgetShadow}
                    onChange={setWidgetShadow}
                  />

                  <Checkbox
                    label={t('admin.widget.animation')}
                    checked={widgetAnimation}
                    onChange={setWidgetAnimation}
                  />

                  <Checkbox
                    label={t('admin.widget.showPointsOnButton')}
                    checked={showPointsOnButton}
                    onChange={setShowPointsOnButton}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    {t('admin.settings.customizationTitle')}
                  </Text>

                  <TextField
                    label={t('admin.settings.pointsName')}
                    value={pointsName}
                    onChange={setPointsName}
                    placeholder={t('admin.settings.pointsName')}
                    autoComplete="off"
                  />

                  <TextField
                    label={t('admin.settings.welcomeMessage')}
                    value={welcomeMessage}
                    onChange={setWelcomeMessage}
                    multiline={3}
                    placeholder={t('admin.widget.welcomeMessage')}
                    autoComplete="off"
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Text as="h2" variant="headingMd">
                      🎨 Advanced CSS Editor
                    </Text>
                  </div>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Add custom CSS to further customize your BROpoints widget appearance.
                    Use BROpoints-specific CSS variables for consistent theming.
                  </Text>

                  <TextField
                    label="Custom CSS"
                    value={customCSS}
                    onChange={setCustomCSS}
                    multiline={8}
                    placeholder={`/* BROpoints CSS Variables */
:root {
  --loyalty-primary: ${widgetColor};
  --loyalty-secondary: ${widgetSecondaryColor};
  --loyalty-text-color: ${widgetTextColor};
}

/* Custom widget styling */
.loyalty-widget-container {
  /* Your custom styles here */
}

/* BROpoints icon customization */
.bropoints-icon {
  /* Icon styling */
}`}
                    autoComplete="off"
                    helpText="Use CSS variables like --loyalty-primary, --loyalty-secondary, and --bropoints-icon for consistent theming"
                  />

                  <div style={{
                    padding: '12px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    border: '1px solid #e0e0e0'
                  }}>
                    <Text as="h3" variant="headingSm" fontWeight="medium">
                      💡 BROpoints CSS Tips:
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      <Text as="p" variant="bodySm" tone="subdued">
                        • Use <code>.loyalty-widget-container</code> to style the main widget
                      </Text>
                      <Text as="p" variant="bodySm" tone="subdued">
                        • Use <code>.bropoints-icon</code> to customize the BROpoints icon
                      </Text>
                      <Text as="p" variant="bodySm" tone="subdued">
                        • Use <code>--loyalty-primary</code> and <code>--loyalty-secondary</code> variables
                      </Text>
                      <Text as="p" variant="bodySm" tone="subdued">
                        • Target <code>.loyalty-panel</code> for popup styling
                      </Text>
                    </div>
                  </div>
                </BlockStack>
              </Card>

                <Card>
                  <InlineStack align="end">
                    <Button variant="primary" onClick={handleSubmit}>
                      {t('admin.settings.saveButton')}
                    </Button>
                  </InlineStack>
                </Card>
              </BlockStack>
            </Layout.Section>

            <Layout.Section >
              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">
                    🎯 {t('admin.widget.preview')}
                  </Text>
                  <Text as="p" variant="bodySm" tone="subdued">
                    {t('admin.widget.previewDescription')}
                  </Text>

                  <WidgetSimulator
                    primaryColor={widgetColor}
                    secondaryColor={widgetSecondaryColor}
                    textColor={widgetTextColor}
                    position={widgetPosition}
                    programName={pointsName || t('admin.widget.loyaltyProgram')}
                    programDescription={t('admin.widget.previewDescription')}
                    welcomeMessage={welcomeMessage || t('admin.widget.welcomeMessage')}
                    customerData={customerData}
                    widgetSize={widgetSize}
                    widgetBorderRadius={widgetBorderRadius}
                    widgetShadow={widgetShadow}
                    widgetAnimation={widgetAnimation}
                    showPointsOnButton={showPointsOnButton}
                  />
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
          {toastMarkup}

          {/* Custom Preset Modal */}
          <Modal
            open={showPresetModal}
            onClose={() => setShowPresetModal(false)}
            title={t('admin.widget.brandPresets.saveAsPreset')}
            primaryAction={{
              content: t('common.save'),
              onAction: handleSaveAsPreset,
              disabled: !presetName.trim()
            }}
            secondaryActions={[{
              content: t('common.cancel'),
              onAction: () => setShowPresetModal(false)
            }]}
          >
            <Modal.Section>
              <FormLayout>
                <TextField
                  label={t('admin.widget.brandPresets.presetName')}
                  value={presetName}
                  onChange={setPresetName}
                  autoComplete="off"
                  placeholder="My Custom Preset"
                />
                <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                  <Text as="p" variant="bodyMd">Preview:</Text>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <div
                      style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '4px',
                        backgroundColor: widgetColor,
                        border: '1px solid #ddd'
                      }}
                    />
                    <div
                      style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '4px',
                        backgroundColor: widgetSecondaryColor,
                        border: '1px solid #ddd'
                      }}
                    />
                    <div
                      style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '4px',
                        backgroundColor: widgetTextColor,
                        border: '1px solid #ddd'
                      }}
                    />
                  </div>
                </div>
              </FormLayout>
            </Modal.Section>
          </Modal>
        </Page>
      </AdminLayout>
    </Frame>
  );
}
