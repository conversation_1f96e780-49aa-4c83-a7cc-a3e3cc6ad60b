import prisma from "../db.server";

export interface SiteSettingsData {
  shopName: string;
  currency: string;
  language: string;
  emailNotifications: boolean;
  senderEmail: string;
  widgetPosition: string;
  widgetColor: string;
  widgetSecondaryColor: string;
  widgetTextColor: string;
  pointsName: string;
  welcomeMessage: string;
  // Nouvelles options de personnalisation
  widgetSize: string;
  widgetBorderRadius: string;
  widgetShadow: boolean;
  widgetAnimation: boolean;
  showPointsOnButton: boolean;
  customCSS: string;
}

/**
 * Récupérer les paramètres du site depuis la table Settings
 */
export async function getSiteSettings(shop: string): Promise<SiteSettingsData | null> {
  try {
    const settings = await prisma.settings.findUnique({
      where: { shop }
    });

    if (!settings) {
      return null;
    }

    return {
      shopName: settings.shopName || shop,
      currency: settings.currency || "EUR",
      language: settings.language || "en",
      emailNotifications: settings.emailNotifications,
      senderEmail: settings.senderEmail || "",
      widgetPosition: settings.widgetPosition || "bottom-right",
      widgetColor: settings.primaryColor || "#2E7D32",
      widgetSecondaryColor: settings.widgetSecondaryColor || "#4CAF50",
      widgetTextColor: settings.widgetTextColor || "#FFFFFF",
      pointsName: settings.pointsName || "Points",
      welcomeMessage: settings.welcomeMessage || "Welcome to our loyalty program!",
      // Paramètres de style avancés
      widgetSize: settings.widgetSize || "medium",
      widgetBorderRadius: settings.widgetBorderRadius || "rounded",
      widgetShadow: settings.widgetShadow,
      widgetAnimation: settings.widgetAnimation,
      showPointsOnButton: settings.showPointsOnButton,
      customCSS: settings.customCSS || ""
    };
  } catch (error) {
    console.error("Error fetching site settings:", error);
    return null;
  }
}

/**
 * Créer ou mettre à jour les paramètres du site
 */
export async function upsertSiteSettings(
  shop: string,
  data: Partial<SiteSettingsData>
): Promise<SiteSettingsData | null> {
  try {
    // Récupérer les paramètres actuels
    const currentSettings = await prisma.settings.findUnique({
      where: { shop }
    });

    // Préparer les données à mettre à jour
    const updateData: any = {};

    // Paramètres de base
    if (data.language) updateData.language = data.language;
    if (data.widgetColor) updateData.primaryColor = data.widgetColor;

    // Paramètres de style du widget
    if (data.widgetSecondaryColor) updateData.widgetSecondaryColor = data.widgetSecondaryColor;
    if (data.widgetTextColor) updateData.widgetTextColor = data.widgetTextColor;
    if (data.widgetPosition) updateData.widgetPosition = data.widgetPosition;
    if (data.widgetSize) updateData.widgetSize = data.widgetSize;
    if (data.widgetBorderRadius) updateData.widgetBorderRadius = data.widgetBorderRadius;
    if (data.widgetShadow !== undefined) updateData.widgetShadow = data.widgetShadow;
    if (data.widgetAnimation !== undefined) updateData.widgetAnimation = data.widgetAnimation;
    if (data.showPointsOnButton !== undefined) updateData.showPointsOnButton = data.showPointsOnButton;

    // Paramètres de contenu
    if (data.pointsName) updateData.pointsName = data.pointsName;
    if (data.welcomeMessage) updateData.welcomeMessage = data.welcomeMessage;
    if (data.shopName) updateData.shopName = data.shopName;
    if (data.currency) updateData.currency = data.currency;
    if (data.customCSS) updateData.customCSS = data.customCSS;
    if (data.emailNotifications !== undefined) updateData.emailNotifications = data.emailNotifications;
    if (data.senderEmail) updateData.senderEmail = data.senderEmail;

    // Mettre à jour ou créer les paramètres
    const settings = await prisma.settings.upsert({
      where: { shop },
      update: updateData,
      create: {
        shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        expirationDays: 365,
        referralPoints: 100,
        birthdayPoints: 250,
        widgetEnabled: true,
        primaryColor: data.widgetColor || "#2E7D32",
        language: data.language || "en",

        // Paramètres de style par défaut
        widgetSecondaryColor: data.widgetSecondaryColor || "#4CAF50",
        widgetTextColor: data.widgetTextColor || "#FFFFFF",
        widgetPosition: data.widgetPosition || "bottom-right",
        widgetSize: data.widgetSize || "medium",
        widgetBorderRadius: data.widgetBorderRadius || "rounded",
        widgetShadow: data.widgetShadow !== undefined ? data.widgetShadow : true,
        widgetAnimation: data.widgetAnimation !== undefined ? data.widgetAnimation : true,
        showPointsOnButton: data.showPointsOnButton !== undefined ? data.showPointsOnButton : true,

        // Paramètres de contenu par défaut
        pointsName: data.pointsName || "Points",
        welcomeMessage: data.welcomeMessage || "Welcome to our loyalty program!",
        shopName: data.shopName || shop,
        currency: data.currency || "EUR",
        customCSS: data.customCSS || "",
        emailNotifications: data.emailNotifications !== undefined ? data.emailNotifications : true,
        senderEmail: data.senderEmail || ""
      }
    });

    // Retourner les paramètres formatés depuis la base de données
    return {
      shopName: settings.shopName || shop,
      currency: settings.currency || "EUR",
      language: settings.language || "en",
      emailNotifications: settings.emailNotifications,
      senderEmail: settings.senderEmail || "",
      widgetPosition: settings.widgetPosition || "bottom-right",
      widgetColor: settings.primaryColor || "#2E7D32",
      widgetSecondaryColor: settings.widgetSecondaryColor || "#4CAF50",
      widgetTextColor: settings.widgetTextColor || "#FFFFFF",
      pointsName: settings.pointsName || "Points",
      welcomeMessage: settings.welcomeMessage || "Welcome to our loyalty program!",
      // Paramètres de style avancés
      widgetSize: settings.widgetSize || "medium",
      widgetBorderRadius: settings.widgetBorderRadius || "rounded",
      widgetShadow: settings.widgetShadow,
      widgetAnimation: settings.widgetAnimation,
      showPointsOnButton: settings.showPointsOnButton,
      customCSS: settings.customCSS || ""
    };
  } catch (error) {
    console.error("Error upserting site settings:", error);
    return null;
  }
}

/**
 * Obtenir les paramètres par défaut
 */
export function getDefaultSiteSettings(shop: string): SiteSettingsData {
  return {
    shopName: shop,
    currency: "EUR",
    language: "en",
    emailNotifications: true,
    senderEmail: "",
    widgetPosition: "bottom-right",
    widgetColor: "#2E7D32",
    widgetSecondaryColor: "#4CAF50",
    widgetTextColor: "#FFFFFF",
    pointsName: "Points",
    welcomeMessage: "Welcome to our loyalty program! Earn points with every purchase.",
    // Nouvelles options par défaut
    widgetSize: "medium",
    widgetBorderRadius: "rounded",
    widgetShadow: true,
    widgetAnimation: true,
    showPointsOnButton: true,
    customCSS: ""
  };
}
