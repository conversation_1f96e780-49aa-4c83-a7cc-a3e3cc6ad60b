import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  BlockStack,
  Text,
  Banner,
  Box,
  InlineStack,
  Icon,
  DataTable,
  Badge,
  Modal,
  Checkbox,
  ButtonGroup,
  Toast,
  Frame,
} from "@shopify/polaris";
import { StarIcon, EditIcon, DeleteIcon, PlusIcon } from "@shopify/polaris-icons";
import { AdminLayout } from "app/components/Layout/AdminLayout";
import { authenticate } from "app/shopify.server";
import {
  getVIPLevels,
  getLevelStatistics,
  initializeBROPointsLevels,
  getVIPProgramSettings,
  updateVIPProgramSettings,
  createVIPLevel,
  updateVIPLevel,
  deleteVIPLevel
} from "app/models/VIPLevel.server";
import { useTranslation } from "app/hooks/useTranslation";
import { useState, useCallback } from "react";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;

  try {
    // Initialize BROpoints levels if they don't exist
    await initializeBROPointsLevels(shop);

    // Get current levels, statistics, and program settings
    const levels = await getVIPLevels(shop);
    const statistics = await getLevelStatistics(shop);
    const programSettings = await getVIPProgramSettings(shop);

    const settings = {
      enabled: programSettings.enabled,
      levels: levels.map(level => ({
        id: level.id,
        name: level.name,
        threshold: level.threshold,
        pointsMultiplier: level.pointsMultiplier,
        benefits: level.benefits,
      })),
      statistics,
      retentionPeriod: programSettings.retentionPeriod,
      evaluationPeriod: programSettings.evaluationPeriod,
    };

    return json({ settings, success: true, error: null });
  } catch (error) {
    console.error("Error loading VIP program data:", error);
    return json({
      settings: {
        enabled: false,
        levels: [],
        statistics: {},
        retentionPeriod: 365,
        evaluationPeriod: 90
      },
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const shop = session.shop;
  const formData = await request.formData();
  const action = formData.get("_action") as string;

  try {
    switch (action) {
      case "updateSettings": {
        const enabled = formData.get("enabled") === "true";
        const retentionPeriod = parseInt(formData.get("retentionPeriod") as string) || 365;
        const evaluationPeriod = parseInt(formData.get("evaluationPeriod") as string) || 90;

        const success = await updateVIPProgramSettings(shop, {
          enabled,
          retentionPeriod,
          evaluationPeriod
        });

        return json({
          success,
          message: success ? "Settings saved successfully" : "Error saving settings"
        });
      }

      case "createLevel": {
        const name = formData.get("name") as string;
        const threshold = parseFloat(formData.get("threshold") as string);
        const pointsMultiplier = parseFloat(formData.get("pointsMultiplier") as string);
        const benefits = formData.get("benefits") as string;

        if (!name || isNaN(threshold) || isNaN(pointsMultiplier)) {
          return json({ success: false, message: "All fields are required and must be valid" });
        }

        const level = await createVIPLevel({
          shop,
          name,
          threshold,
          pointsMultiplier,
          benefits: benefits || ""
        });

        return json({
          success: !!level,
          message: level ? "Level created successfully" : "Error creating level"
        });
      }

      case "updateLevel": {
        const id = formData.get("id") as string;
        const name = formData.get("name") as string;
        const threshold = parseFloat(formData.get("threshold") as string);
        const pointsMultiplier = parseFloat(formData.get("pointsMultiplier") as string);
        const benefits = formData.get("benefits") as string;

        if (!id || !name || isNaN(threshold) || isNaN(pointsMultiplier)) {
          return json({ success: false, message: "All fields are required and must be valid" });
        }

        const level = await updateVIPLevel(id, {
          name,
          threshold,
          pointsMultiplier,
          benefits: benefits || ""
        });

        return json({
          success: !!level,
          message: level ? "Level updated successfully" : "Error updating level"
        });
      }

      case "deleteLevel": {
        const id = formData.get("id") as string;

        if (!id) {
          return json({ success: false, message: "Level ID is required" });
        }

        const success = await deleteVIPLevel(id);

        return json({
          success,
          message: success ? "Level deleted successfully" : "Error deleting level"
        });
      }

      default:
        return json({ success: false, message: "Invalid action" });
    }
  } catch (error) {
    console.error("Error in VIP program action:", error);
    return json({
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

export default function ProgramVIP() {
  const { settings, success, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const { t } = useTranslation();

  // State for modals and forms
  const [showLevelModal, setShowLevelModal] = useState(false);
  const [editingLevel, setEditingLevel] = useState<any>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingLevel, setDeletingLevel] = useState<any>(null);
  const [toastMessage, setToastMessage] = useState<string>("");
  const [showToast, setShowToast] = useState(false);

  // Form state for level editing
  const [levelForm, setLevelForm] = useState({
    name: "",
    threshold: "",
    pointsMultiplier: "",
    benefits: ""
  });

  // Program settings state
  const [programEnabled, setProgramEnabled] = useState(settings.enabled);
  const [retentionPeriod, setRetentionPeriod] = useState(settings.retentionPeriod.toString());
  const [evaluationPeriod, setEvaluationPeriod] = useState(settings.evaluationPeriod.toString());

  const isLoading = navigation.state === "submitting";

  const handleToggleProgram = useCallback(() => {
    const formData = new FormData();
    formData.append("_action", "updateSettings");
    formData.append("enabled", (!programEnabled).toString());
    formData.append("retentionPeriod", retentionPeriod);
    formData.append("evaluationPeriod", evaluationPeriod);

    submit(formData, { method: "post" });
    setProgramEnabled(!programEnabled);
  }, [programEnabled, retentionPeriod, evaluationPeriod, submit]);

  const handleSaveSettings = useCallback(() => {
    const formData = new FormData();
    formData.append("_action", "updateSettings");
    formData.append("enabled", programEnabled.toString());
    formData.append("retentionPeriod", retentionPeriod);
    formData.append("evaluationPeriod", evaluationPeriod);

    submit(formData, { method: "post" });
  }, [programEnabled, retentionPeriod, evaluationPeriod, submit]);

  const handleEditLevel = useCallback((level: any) => {
    setEditingLevel(level);
    setLevelForm({
      name: level.name,
      threshold: level.threshold.toString(),
      pointsMultiplier: level.pointsMultiplier.toString(),
      benefits: level.benefits
    });
    setShowLevelModal(true);
  }, []);

  const handleAddLevel = useCallback(() => {
    setEditingLevel(null);
    setLevelForm({
      name: "",
      threshold: "",
      pointsMultiplier: "",
      benefits: ""
    });
    setShowLevelModal(true);
  }, []);

  const handleDeleteLevel = useCallback((level: any) => {
    setDeletingLevel(level);
    setShowDeleteModal(true);
  }, []);

  const handleSaveLevel = useCallback(() => {
    const formData = new FormData();
    formData.append("_action", editingLevel ? "updateLevel" : "createLevel");

    if (editingLevel) {
      formData.append("id", editingLevel.id);
    }

    formData.append("name", levelForm.name);
    formData.append("threshold", levelForm.threshold);
    formData.append("pointsMultiplier", levelForm.pointsMultiplier);
    formData.append("benefits", levelForm.benefits);

    submit(formData, { method: "post" });
    setShowLevelModal(false);
  }, [editingLevel, levelForm, submit]);

  const handleConfirmDelete = useCallback(() => {
    if (deletingLevel) {
      const formData = new FormData();
      formData.append("_action", "deleteLevel");
      formData.append("id", deletingLevel.id);

      submit(formData, { method: "post" });
      setShowDeleteModal(false);
      setDeletingLevel(null);
    }
  }, [deletingLevel, submit]);

  const rows = settings.levels.map((level: any) => [
    <Text as="span" variant="bodyMd" fontWeight="bold">{t(`loyalty.vip.levels.${level.name}`) || level.name}</Text>,
    <Text as="span" variant="bodyMd">{level.threshold} {t('admin.vipProgram.levels.threshold')}</Text>,
    <Text as="span" variant="bodyMd">x{level.pointsMultiplier}</Text>,
    <Text as="span" variant="bodyMd">{level.benefits.split('\n').join(', ')}</Text>,
    <ButtonGroup>
      <Button size="slim" icon={EditIcon} onClick={() => handleEditLevel(level)}>
        {t('common.edit')}
      </Button>
      <Button size="slim" icon={DeleteIcon} tone="critical" onClick={() => handleDeleteLevel(level)}>
        {t('common.delete')}
      </Button>
    </ButtonGroup>
  ]);

  const toastMarkup = showToast ? (
    <Toast content={toastMessage} onDismiss={() => setShowToast(false)} />
  ) : null;

  return (
    <Frame>
      {toastMarkup}
      <AdminLayout title={t('admin.vipProgram.title')}>
        <Layout>
          <Layout.Section>
            <BlockStack gap="500">
              {error && (
                <Banner tone="critical">
                  <Text as="p" variant="bodyMd">
                    {t('admin.vipProgram.messages.errorLoading')}: {error}
                  </Text>
                </Banner>
              )}

              <Banner tone="info">
                <Text as="p" variant="bodyMd">
                  {t('admin.vipProgram.description')}
                </Text>
              </Banner>

              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                      <Text as="h2" variant="headingMd">{t('admin.vipProgram.programStatus')}</Text>
                      <Badge tone={programEnabled ? "success" : "attention"}>
                        {programEnabled ? t('admin.vipProgram.active') : t('admin.vipProgram.inactive')}
                      </Badge>
                    </div>
                    <Button
                      tone={programEnabled ? "critical" : "success"}
                      onClick={handleToggleProgram}
                      loading={isLoading}
                    >
                      {programEnabled ? t('admin.vipProgram.disable') : t('admin.vipProgram.enable')}
                    </Button>
                  </div>
                  <Text as="p" variant="bodyMd" tone="subdued">
                    {programEnabled ? t('admin.vipProgram.enableDescription') : t('admin.vipProgram.disableDescription')}
                  </Text>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <div>
                      <Text as="h2" variant="headingMd">{t('admin.vipProgram.levels.title')}</Text>
                      <Box paddingBlockStart="200">
                        <InlineStack gap="200" align="center">
                          <Icon source={StarIcon} />
                          <Text as="span" variant="bodyMd">
                            {t('admin.vipProgram.levels.configured', { count: settings.levels.length })}
                          </Text>
                        </InlineStack>
                      </Box>
                    </div>
                    <Button
                      variant="primary"
                      icon={PlusIcon}
                      onClick={handleAddLevel}
                      disabled={!programEnabled}
                    >
                      {t('admin.vipProgram.levels.addLevel')}
                    </Button>
                  </div>

                  <DataTable
                    columnContentTypes={["text", "text", "text", "text", "text"]}
                    headings={[
                      t('admin.vipProgram.levels.name'),
                      t('admin.vipProgram.levels.threshold'),
                      t('admin.vipProgram.levels.multiplier'),
                      t('admin.vipProgram.levels.benefits'),
                      t('admin.vipProgram.levels.actions')
                    ]}
                    rows={rows}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="400">
                  <Text as="h2" variant="headingMd">{t('admin.vipProgram.evaluation.title')}</Text>
                  <Text as="p" variant="bodyMd">
                    {t('admin.vipProgram.evaluation.description')}
                  </Text>
                  <FormLayout>
                    <TextField
                      label={t('admin.vipProgram.evaluation.retentionPeriod')}
                      type="number"
                      value={retentionPeriod}
                      onChange={setRetentionPeriod}
                      autoComplete="off"
                      helpText={t('admin.vipProgram.evaluation.retentionPeriodHelp')}
                      min={0}
                      disabled={!programEnabled}
                    />
                    <TextField
                      label={t('admin.vipProgram.evaluation.evaluationPeriod')}
                      type="number"
                      value={evaluationPeriod}
                      onChange={setEvaluationPeriod}
                      autoComplete="off"
                      helpText={t('admin.vipProgram.evaluation.evaluationPeriodHelp')}
                      min={0}
                      disabled={!programEnabled}
                    />
                    <Button
                      variant="primary"
                      onClick={handleSaveSettings}
                      loading={isLoading}
                      disabled={!programEnabled}
                    >
                      {t('admin.vipProgram.evaluation.saveSettings')}
                    </Button>
                  </FormLayout>
                </BlockStack>
              </Card>
          </BlockStack>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="500">
            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.vipProgram.statistics.title')}</Text>
                <BlockStack gap="200">
                  {settings.levels.map((level: any) => (
                    <Text key={level.id} as="p" variant="bodyMd">
                      {t('admin.vipProgram.statistics.customers', {
                        level: t(`loyalty.vip.levels.${level.name}`) || level.name,
                        count: (settings.statistics as any)[level.name] || 0
                      })}
                    </Text>
                  ))}
                </BlockStack>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="400">
                <Text as="h2" variant="headingMd">{t('admin.vipProgram.help.title')}</Text>
                <Text as="p" variant="bodyMd">
                  {t('admin.vipProgram.help.description')}
                </Text>
                <Text as="p" variant="bodyMd">
                  {t('admin.vipProgram.help.progression')}
                </Text>
                <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                  <BlockStack gap="200">
                    <Text as="p" variant="bodyMd" fontWeight="bold">Level Information:</Text>
                    {settings.levels.map((level: any) => (
                      <Text key={level.id} as="p" variant="bodySm">
                        • {t(`loyalty.vip.levels.${level.name}`) || level.name} ({level.threshold} points): {level.pointsMultiplier}x multiplier
                      </Text>
                    ))}
                  </BlockStack>
                </Box>
              </BlockStack>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>

      {/* Level Modal */}
      <Modal
        open={showLevelModal}
        onClose={() => setShowLevelModal(false)}
        title={editingLevel ? t('admin.vipProgram.levels.editLevel') : t('admin.vipProgram.levels.addLevel')}
        primaryAction={{
          content: t('admin.vipProgram.levels.save'),
          onAction: handleSaveLevel,
          loading: isLoading
        }}
        secondaryActions={[{
          content: t('admin.vipProgram.levels.cancel'),
          onAction: () => setShowLevelModal(false)
        }]}
      >
        <Modal.Section>
          <FormLayout>
            <TextField
              label={t('admin.vipProgram.levels.name')}
              value={levelForm.name}
              onChange={(value) => setLevelForm({...levelForm, name: value})}
              autoComplete="off"
              error={!levelForm.name ? t('admin.vipProgram.levels.nameRequired') : undefined}
            />
            <TextField
              label={t('admin.vipProgram.levels.threshold')}
              type="number"
              value={levelForm.threshold}
              onChange={(value) => setLevelForm({...levelForm, threshold: value})}
              autoComplete="off"
              helpText={t('admin.vipProgram.levels.thresholdHelp')}
              min={0}
              error={!levelForm.threshold || isNaN(Number(levelForm.threshold)) ? t('admin.vipProgram.levels.thresholdRequired') : undefined}
            />
            <TextField
              label={t('admin.vipProgram.levels.multiplier')}
              type="number"
              value={levelForm.pointsMultiplier}
              onChange={(value) => setLevelForm({...levelForm, pointsMultiplier: value})}
              autoComplete="off"
              helpText={t('admin.vipProgram.levels.multiplierHelp')}
              min={0}
              step={0.01}
              error={!levelForm.pointsMultiplier || isNaN(Number(levelForm.pointsMultiplier)) ? t('admin.vipProgram.levels.multiplierRequired') : undefined}
            />
            <TextField
              label={t('admin.vipProgram.levels.benefits')}
              value={levelForm.benefits}
              onChange={(value) => setLevelForm({...levelForm, benefits: value})}
              multiline={3}
              autoComplete="off"
              helpText={t('admin.vipProgram.levels.benefitsHelp')}
            />
          </FormLayout>
        </Modal.Section>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('admin.vipProgram.levels.deleteLevel')}
        primaryAction={{
          content: t('common.delete'),
          onAction: handleConfirmDelete,
          destructive: true,
          loading: isLoading
        }}
        secondaryActions={[{
          content: t('common.cancel'),
          onAction: () => setShowDeleteModal(false)
        }]}
      >
        <Modal.Section>
          <Text as="p" variant="bodyMd">
            {t('admin.vipProgram.levels.confirmDelete')}
          </Text>
          <Text as="p" variant="bodyMd" tone="subdued">
            {t('admin.vipProgram.levels.deleteWarning')}
          </Text>
        </Modal.Section>
      </Modal>
    </AdminLayout>
    </Frame>
  );
}