# 📧 Système d'Emails - Custom Loyalty Rewards App

## Vue d'ensemble

Le système d'emails automatiques envoie des notifications aux clients lors d'événements importants du programme de fidélité. Il utilise **Nodemailer** avec support de multiples fournisseurs (Gmail, Outlook, SMTP personnalisé, SendGrid) et propose des templates HTML responsives.

---

## 🚀 Configuration Rapide

### 1. Variables d'Environnement

Copiez `.env.email.example` vers `.env` et configurez selon votre fournisseur :

#### Option A : SMTP Personnalisé (Recommandé)
```bash
EMAIL_PROVIDER=smtp
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=BROpoints
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=votre-mot-de-passe-app
EMAIL_ENABLED=true
```

#### Option B : Gmail OAuth2
```bash
EMAIL_PROVIDER=gmail
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=BROpoints
EMAIL_CLIENT_ID=votre_client_id.googleusercontent.com
EMAIL_CLIENT_SECRET=votre_client_secret
EMAIL_REFRESH_TOKEN=votre_refresh_token
EMAIL_ENABLED=true
```

#### Option C : SendGrid (Legacy)
```bash
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=SG.votre_cle_api_sendgrid
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=BROpoints
EMAIL_ENABLED=true
```

### 2. Configuration par Fournisseur

#### Gmail (SMTP avec mot de passe d'application)
1. **Activez la 2FA** sur votre compte Google
2. **Générez un mot de passe d'application** dans les paramètres Google
3. **Utilisez ce mot de passe** dans `SMTP_PASSWORD`

#### Outlook/Hotmail
1. **Utilisez les paramètres SMTP** : `smtp-mail.outlook.com:587`
2. **Activez l'authentification** à deux facteurs si nécessaire

#### SMTP Personnalisé
1. **Obtenez les paramètres** de votre fournisseur d'hébergement
2. **Configurez les variables** SMTP selon la documentation

#### SendGrid
1. **Créer un compte SendGrid** (gratuit jusqu'à 100 emails/jour)
2. **Obtenir une clé API** avec permissions "Mail Send"
3. **Vérifier l'adresse d'expédition** dans SendGrid

### 3. Activation

1. Allez dans **Admin > Paramètres > Configuration des Emails**
2. Vérifiez que le statut est "Configuré"
3. Activez les notifications par email
4. Envoyez un email de test

### 4. Avantages de Nodemailer

- ✅ **Multi-fournisseurs** : Gmail, Outlook, SMTP, SendGrid
- ✅ **Flexibilité** : OAuth2 ou authentification simple
- ✅ **Fiabilité** : Gestion automatique des erreurs et retry
- ✅ **Sécurité** : Support TLS/SSL natif
- ✅ **Performance** : Pool de connexions réutilisables

---

## 📨 Types d'Emails Automatiques

### 1. Email de Bienvenue
- **Déclencheur :** Inscription au programme de fidélité
- **Contenu :** Message de bienvenue + points de bienvenue
- **Template :** `WELCOME`

### 2. Points Gagnés
- **Déclencheur :** Attribution de points (achat, parrainage, etc.)
- **Contenu :** Nombre de points gagnés + nouveau solde
- **Template :** `POINTS_EARNED`

### 3. Points Utilisés
- **Déclencheur :** Échange de points contre récompense
- **Contenu :** Points utilisés + récompense obtenue
- **Template :** `POINTS_REDEEMED`

### 4. Promotion de Niveau
- **Déclencheur :** Changement de niveau VIP
- **Contenu :** Nouveau niveau + avantages
- **Template :** `LEVEL_UP`

### 5. Récompense de Parrainage
- **Déclencheur :** Parrainage réussi
- **Contenu :** Points gagnés + nom du filleul
- **Template :** `REFERRAL_REWARD`

### 6. Rappel Mensuel (à implémenter)
- **Déclencheur :** Tâche cron mensuelle
- **Contenu :** Solde actuel + suggestions
- **Template :** `MONTHLY_REMINDER`

---

## 🛠️ Architecture Technique

### Services Principaux

```
app/services/
├── emailService.server.ts      # Service d'envoi via SendGrid
├── emailTriggers.server.ts     # Déclencheurs automatiques
└── pointsService.server.ts     # Intégration avec les points
```

### Intégrations

Le système s'intègre automatiquement avec :
- **Service de points** (`pointsService.server.ts`)
- **Service de coupons** (`loyaltyCoupons.server.ts`)
- **Système de parrainage** (`Referral.server.ts`)

### Base de Données

Les paramètres d'emails sont stockés dans la table `Settings` :
```sql
emailNotifications: boolean  -- Activer/désactiver
shopName: string            -- Nom dans les emails
pointsName: string          -- Nom des points
```

---

## 🎨 Templates d'Emails

### Structure HTML

Tous les templates utilisent :
- **Design responsive** (mobile-first)
- **Couleurs de marque** (vert BROpoints)
- **Typographie cohérente**
- **Boutons d'action clairs**

### Variables Disponibles

Chaque template reçoit des variables contextuelles :

```javascript
// Email de bienvenue
{
  customerName: "John Doe",
  welcomePoints: 100,
  shopName: "Ma Boutique",
  shopUrl: "https://boutique.com",
  pointsName: "BROpoints"
}

// Points gagnés
{
  customerName: "John Doe",
  points: 50,
  totalPoints: 550,
  reason: "Commande #1234",
  shopName: "Ma Boutique",
  pointsName: "BROpoints"
}
```

### Personnalisation

Pour personnaliser les templates :
1. Modifiez les fonctions dans `emailService.server.ts`
2. Adaptez le HTML et CSS selon vos besoins
3. Testez avec le script de test

---

## 🧪 Tests et Débogage

### Script de Test

```bash
# Tester le système complet
node scripts/test-email-system.mjs

# Variables d'environnement pour les tests
TEST_EMAIL=<EMAIL>
```

### Logs et Monitoring

Les emails sont loggés avec :
- **Succès :** ID du message SendGrid
- **Erreurs :** Code d'erreur et détails
- **Données :** Informations du destinataire

### Débogage Courant

```javascript
// Vérifier la configuration
import { isEmailServiceConfigured } from './emailService.server';
console.log('Emails configurés:', isEmailServiceConfigured());

// Tester un envoi manuel
import { sendEmail, EmailType } from './emailService.server';
const result = await sendEmail(EmailType.WELCOME, {
  to: '<EMAIL>',
  templateData: { customerName: 'Test' }
});
```

---

## 🔧 Interface d'Administration

### Page de Configuration

**URL :** `/app/settings/emails`

**Fonctionnalités :**
- ✅ Statut de la configuration
- ✅ Activation/désactivation des emails
- ✅ Configuration du nom de boutique
- ✅ Configuration du nom des points
- ✅ Liste des types d'emails
- ✅ Envoi d'email de test

### Indicateurs de Statut

- 🟢 **Configuré** : Service prêt à envoyer
- 🔴 **Non configuré** : Variables manquantes
- 🟡 **Désactivé** : Service configuré mais inactif

---

## 📊 Métriques et Analytics

### Données Trackées

- **Emails envoyés** par type
- **Taux de succès** d'envoi
- **Erreurs** et codes de retour
- **Performance** (temps de réponse)

### Intégration SendGrid

SendGrid fournit automatiquement :
- **Statistiques de livraison**
- **Taux d'ouverture**
- **Clics sur les liens**
- **Désabonnements**

---

## 🚨 Gestion des Erreurs

### Erreurs Courantes

| Erreur | Cause | Solution |
|--------|-------|----------|
| `Service email non configuré` | Variables manquantes | Vérifier `.env` |
| `Erreur SendGrid: 401` | Clé API invalide | Régénérer la clé |
| `Erreur SendGrid: 403` | Email non vérifié | Vérifier dans SendGrid |
| `Client non trouvé` | ID client invalide | Vérifier la base de données |

### Stratégie de Retry

Les emails échoués sont :
1. **Loggés** avec détails de l'erreur
2. **Non re-tentés** automatiquement (éviter le spam)
3. **Reportés** dans les logs pour investigation

---

## 🔒 Sécurité et Conformité

### Protection des Données

- **Emails chiffrés** en transit (TLS)
- **Clés API** stockées en variables d'environnement
- **Logs** sans données sensibles
- **Désabonnement** respecté

### Conformité RGPD

- **Consentement** via activation des notifications
- **Droit à l'oubli** via suppression du compte
- **Transparence** sur les types d'emails envoyés

### Limites et Quotas

- **SendGrid gratuit :** 100 emails/jour
- **Rate limiting :** Respecté automatiquement
- **Monitoring :** Surveillance du quota

---

## 🔄 Maintenance et Évolution

### Ajout de Nouveaux Types d'Emails

1. **Ajouter** le type dans `EmailType` enum
2. **Créer** le template HTML/text
3. **Implémenter** la fonction de déclenchement
4. **Intégrer** dans le code métier
5. **Tester** avec le script de test

### Mise à Jour des Templates

1. **Modifier** les fonctions de template
2. **Tester** l'affichage sur différents clients email
3. **Valider** la responsivité mobile
4. **Déployer** et monitorer

### Optimisations Futures

- **Templates visuels** avec éditeur WYSIWYG
- **A/B testing** des contenus
- **Personnalisation avancée** par segment
- **Analytics détaillées** intégrées
- **Automation workflows** complexes

---

## 📞 Support et Ressources

### Documentation SendGrid
- [API Reference](https://docs.sendgrid.com/api-reference)
- [Email Templates](https://docs.sendgrid.com/ui/sending-email/how-to-send-an-email-with-dynamic-transactional-templates)

### Outils de Test
- [Email Tester](https://www.mail-tester.com/)
- [Litmus](https://www.litmus.com/)
- [Email on Acid](https://www.emailonacid.com/)

### Contact Support
Pour toute assistance technique, contactez l'équipe de développement avec :
- Logs d'erreur complets
- Configuration (sans les clés API)
- Description du problème
- Étapes de reproduction
