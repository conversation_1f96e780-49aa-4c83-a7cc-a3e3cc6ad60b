/**
 * Script de test simple pour vérifier la création de commandes
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testOrderCreation() {
  console.log('🧪 Test de création de commande...');
  
  try {
    const shop = "test-shop.myshopify.com";

    // 0. Créer les paramètres du shop (requis pour la contrainte de clé étrangère)
    await prisma.settings.upsert({
      where: { shop: shop },
      update: {},
      create: {
        shop: shop,
        earningRate: 1.0,
        redemptionRate: 100.0,
        minimumPoints: 100,
        pointsName: "Points",
        welcomeMessage: "Bienvenue dans notre programme de fidélité de test !"
      }
    });

    console.log('✅ Paramètres du shop créés/mis à jour');

    // 1. Créer un client de test
    const customer = await prisma.customer.create({
      data: {
        customerId: "test-customer-789",
        shop: shop,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        type: "guest",
        points: 0,
        totalSpent: 0,
        ordersCount: 0
      }
    });
    
    console.log('✅ Client créé:', customer.firstName, customer.lastName);

    // 2. Créer une commande de test
    const orderData = {
      orderId: "test-order-456",
      customerDbId: customer.id,
      shopifyCustomerId: customer.customerId,
      shop: shop,
      total: 89.99,
      status: "pending",
      paymentStatus: "paid"
    };

    // Importer la fonction upsertOrder
    const { upsertOrder } = await import('../app/models/Order.server.ts');
    
    const order = await upsertOrder(orderData);
    
    if (order) {
      console.log('✅ Commande créée avec succès !');
      console.log(`   - ID: ${order.orderId}`);
      console.log(`   - Total: ${order.total}€`);
      console.log(`   - Statut: ${order.status}`);
      console.log(`   - Client DB ID: ${order.customerDbId}`);
    } else {
      console.log('❌ Échec de la création de commande');
    }

    // 3. Vérifier que la commande existe en base
    const foundOrder = await prisma.order.findFirst({
      where: {
        orderId: orderData.orderId,
        shop: shop
      },
      include: {
        customer: true
      }
    });

    if (foundOrder) {
      console.log('✅ Commande trouvée en base de données !');
      console.log(`   - Client: ${foundOrder.customer.firstName} ${foundOrder.customer.lastName}`);
      console.log(`   - Email: ${foundOrder.customer.email}`);
    } else {
      console.log('❌ Commande non trouvée en base de données');
    }

    // 4. Test de mise à jour de commande
    const updatedOrder = await upsertOrder({
      ...orderData,
      total: 95.50,
      status: "fulfilled"
    });

    if (updatedOrder && updatedOrder.total === 95.50) {
      console.log('✅ Mise à jour de commande réussie !');
      console.log(`   - Nouveau total: ${updatedOrder.total}€`);
      console.log(`   - Nouveau statut: ${updatedOrder.status}`);
    } else {
      console.log('❌ Échec de la mise à jour de commande');
    }

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

// Fonction de nettoyage
async function cleanup() {
  console.log('🧹 Nettoyage des données de test...');
  
  try {
    await prisma.order.deleteMany({
      where: {
        orderId: "test-order-456"
      }
    });

    await prisma.customer.deleteMany({
      where: {
        customerId: "test-customer-789"
      }
    });

    await prisma.settings.deleteMany({
      where: {
        shop: "test-shop.myshopify.com"
      }
    });

    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

// Exécuter le test
async function main() {
  await cleanup(); // Nettoyer d'abord
  await testOrderCreation();
  
  console.log('\n🔍 Voulez-vous nettoyer les données de test ? (Ctrl+C pour garder)');
  
  // Attendre 5 secondes puis nettoyer automatiquement
  setTimeout(async () => {
    await cleanup();
    await prisma.$disconnect();
    process.exit(0);
  }, 5000);
}

main().catch(console.error);
