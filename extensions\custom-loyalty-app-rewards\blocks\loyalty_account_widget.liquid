{% comment %}
  Bloc Liquid : Widget de fidélité pour espace client
  Affiche un résumé complet du programme de fidélité
{% endcomment %}

<div class="loyalty-account-widget" 
     onclick="window.loyaltyWidget && window.loyaltyWidget.open()">
  
  {% if customer %}
    <!-- Client connecté -->
    <div class="loyalty-account-content">
      <div class="loyalty-account-header">
        <div class="loyalty-account-title">
          <h3>BROpoints Program</h3>
          <div class="loyalty-status">
            <span class="status-badge" id="account-vip-level">Member</span>
            <span class="status-text">Active since {{ customer.created_at | date: "%B %Y" }}</span>
          </div>
        </div>
        <div class="loyalty-account-icon bropoints-icon bropoints-icon-large">
          <!-- BROpoints icon applied via CSS -->
        </div>
      </div>
      
      <div class="loyalty-account-stats">
        <div class="stat-card primary">
          <div class="stat-icon">⭐</div>
          <div class="stat-content">
            <div class="stat-value" id="account-points-balance">-- points</div>
            <div class="stat-label">Available points</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🛍️</div>
          <div class="stat-content">
            <div class="stat-value">{{ customer.orders_count | default: 0 }}</div>
            <div class="stat-label">Orders</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-value">{{ customer.total_spent | money }}</div>
            <div class="stat-label">Total spent</div>
          </div>
        </div>
      </div>
      
      <div class="loyalty-account-progress">
        <div class="progress-header">
          <span class="progress-title">Progression vers le niveau suivant</span>
          <span class="progress-info" id="account-next-level">Chargement...</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" id="account-progress-fill" style="width: 0%"></div>
        </div>
        <div class="progress-details">
          <span class="progress-current" id="account-current-spent">{{ customer.total_spent | money }}</span>
          <span class="progress-target" id="account-target-spent">--</span>
        </div>
      </div>
      
      <div class="loyalty-account-actions">
        <div class="action-grid">
          <div class="action-item">
            <div class="action-icon">🎁</div>
            <div class="action-content">
              <div class="action-title">Mes Récompenses</div>
              <div class="action-subtitle">Échanger mes points</div>
            </div>
          </div>
          
          <div class="action-item">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <div class="action-title">Mon Activité</div>
              <div class="action-subtitle">Historique des points</div>
            </div>
          </div>
          
          <div class="action-item">
            <div class="action-icon">👥</div>
            <div class="action-content">
              <div class="action-title">Parrainage</div>
              <div class="action-subtitle">Inviter des amis</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="loyalty-account-benefits">
        <div class="benefits-header">
          <span class="benefits-title">Vos avantages actuels</span>
        </div>
        <div class="benefits-list" id="account-benefits-list">
          <div class="benefit-item">
            <span class="benefit-icon">⭐</span>
            <span class="benefit-text">Points à chaque achat</span>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">🎁</span>
            <span class="benefit-text">Récompenses exclusives</span>
          </div>
          <div class="benefit-item">
            <span class="benefit-icon">👥</span>
            <span class="benefit-text">Bonus de parrainage</span>
          </div>
        </div>
      </div>
      
      <div class="loyalty-account-cta">
        <div class="cta-content">
          <span class="cta-text">Ouvrir mon hub BROpoints</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
    
  {% else %}
    <!-- Client non connecté (ne devrait pas arriver sur la page compte) -->
    <div class="loyalty-account-content guest">
      <div class="loyalty-account-header">
        <div class="loyalty-account-title">
          <h3>Programme BROpoints</h3>
          <p>Rejoignez notre programme de fidélité</p>
        </div>
      </div>
      
      <div class="loyalty-guest-benefits">
        <div class="guest-benefit-item">
          <span class="guest-benefit-icon">⭐</span>
          <span class="guest-benefit-text">Gagnez des points à chaque achat</span>
        </div>
        <div class="guest-benefit-item">
          <span class="guest-benefit-icon">🎁</span>
          <span class="guest-benefit-text">Échangez contre des récompenses</span>
        </div>
        <div class="guest-benefit-item">
          <span class="guest-benefit-icon">👥</span>
          <span class="guest-benefit-text">Parrainez vos amis</span>
        </div>
      </div>
      
      <div class="loyalty-account-cta">
        <div class="cta-content">
          <span class="cta-text">Découvrir le programme</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
  {% endif %}
</div>

<style>
  .loyalty-account-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    padding: 24px;
    margin: 24px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .loyalty-account-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  }
  
  .loyalty-account-widget:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(46, 125, 50, 0.15);
    border-color: #2E7D32;
  }
  
  .loyalty-account-content {
    position: relative;
    z-index: 1;
  }
  
  .loyalty-account-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
  }
  
  .loyalty-account-title h3 {
    margin: 0 0 8px 0;
    color: #2E7D32;
    font-size: 24px;
    font-weight: 700;
  }
  
  .loyalty-status {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .status-badge {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
  }
  
  .status-text {
    color: #6c757d;
    font-size: 14px;
  }
  
  .loyalty-account-icon {
    color: #2E7D32;
    opacity: 0.7;
  }
  
  .loyalty-account-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
  }
  
  .stat-card.primary {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    color: white;
  }
  
  .stat-card:hover {
    transform: translateY(-2px);
  }
  
  .stat-icon {
    font-size: 24px;
    flex-shrink: 0;
  }
  
  .stat-content {
    flex: 1;
  }
  
  .stat-value {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 4px;
  }
  
  .stat-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .loyalty-account-progress {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .progress-title {
    font-weight: 600;
    color: #2E7D32;
    font-size: 14px;
  }
  
  .progress-info {
    font-size: 12px;
    color: #6c757d;
  }
  
  .progress-bar {
    background: #e0e0e0;
    border-radius: 8px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 8px;
  }
  
  .progress-fill {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    height: 100%;
    border-radius: 8px;
    transition: width 0.6s ease;
  }
  
  .progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
  }
  
  .loyalty-account-actions {
    margin-bottom: 24px;
  }
  
  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
  
  .action-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px 12px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .action-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
  }
  
  .action-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  .action-title {
    font-weight: 600;
    color: #2E7D32;
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .action-subtitle {
    font-size: 12px;
    color: #6c757d;
  }
  
  .loyalty-account-benefits {
    margin-bottom: 24px;
  }
  
  .benefits-header {
    margin-bottom: 12px;
  }
  
  .benefits-title {
    font-weight: 600;
    color: #2E7D32;
    font-size: 14px;
  }
  
  .benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
  }
  
  .benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6c757d;
  }
  
  .benefit-icon {
    font-size: 16px;
    flex-shrink: 0;
  }
  
  .loyalty-account-cta {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .loyalty-account-widget:hover .loyalty-account-cta {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3);
  }
  
  .cta-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: white;
    font-weight: 600;
    font-size: 16px;
  }
  
  /* Version invité */
  .loyalty-guest-benefits {
    margin-bottom: 24px;
  }
  
  .guest-benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .guest-benefit-item:last-child {
    border-bottom: none;
  }
  
  .guest-benefit-icon {
    font-size: 20px;
    flex-shrink: 0;
  }
  
  .guest-benefit-text {
    color: #6c757d;
    font-size: 14px;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .loyalty-account-widget {
      padding: 20px;
      margin: 20px 0;
    }
    
    .loyalty-account-header {
      flex-direction: column;
      gap: 12px;
    }
    
    .loyalty-account-title h3 {
      font-size: 20px;
    }
    
    .loyalty-account-stats {
      grid-template-columns: 1fr;
    }
    
    .action-grid {
      grid-template-columns: 1fr;
    }
    
    .benefits-list {
      grid-template-columns: 1fr;
    }
    
    .progress-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
  
  /* Animation au chargement */
  @keyframes loyaltyAccountFadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .loyalty-account-widget {
    animation: loyaltyAccountFadeIn 0.8s ease-out;
  }
</style>

<script>
  // Mettre à jour les informations du compte de fidélité
  document.addEventListener('DOMContentLoaded', function() {
    const accountWidget = document.querySelector('.loyalty-account-widget');
    if (!accountWidget) return;
    
    // Mettre à jour toutes les informations du compte
    function updateAccountInfo() {
      if (window.loyaltyWidget && window.loyaltyWidget.customerData) {
        const customerData = window.loyaltyWidget.customerData;
        
        // Mettre à jour le solde de points
        const pointsElement = document.getElementById('account-points-balance');
        if (pointsElement) {
          pointsElement.textContent = `${customerData.points || 0} points`;
        }
        
        // Mettre à jour le niveau VIP
        const vipLevelElement = document.getElementById('account-vip-level');
        if (vipLevelElement && customerData.vipLevel) {
          vipLevelElement.textContent = customerData.vipLevel;
        }
        
        // Mettre à jour la progression vers le niveau suivant
        updateLevelProgress(customerData);
        
        // Mettre à jour les avantages
        updateBenefits(customerData);
      }
    }
    
    // Mettre à jour la progression de niveau
    function updateLevelProgress(customerData) {
      const nextLevelElement = document.getElementById('account-next-level');
      const progressFillElement = document.getElementById('account-progress-fill');
      const targetSpentElement = document.getElementById('account-target-spent');
      
      if (customerData.nextLevelThreshold) {
        const currentSpent = customerData.totalSpent || 0;
        const targetSpent = customerData.nextLevelThreshold;
        const progress = Math.min((currentSpent / targetSpent) * 100, 100);
        
        if (nextLevelElement) {
          nextLevelElement.textContent = customerData.nextLevel || 'Niveau maximum';
        }
        
        if (progressFillElement) {
          progressFillElement.style.width = `${progress}%`;
        }
        
        if (targetSpentElement) {
          targetSpentElement.textContent = `${targetSpent}€`;
        }
      } else {
        if (nextLevelElement) {
          nextLevelElement.textContent = 'Niveau maximum atteint';
        }
        
        if (progressFillElement) {
          progressFillElement.style.width = '100%';
        }
      }
    }
    
    // Mettre à jour les avantages selon le niveau
    function updateBenefits(customerData) {
      const benefitsListElement = document.getElementById('account-benefits-list');
      if (!benefitsListElement) return;
      
      let benefits = [
        { icon: '⭐', text: 'Points à chaque achat' },
        { icon: '🎁', text: 'Récompenses exclusives' },
        { icon: '👥', text: 'Bonus de parrainage' }
      ];
      
      // Ajouter des avantages selon le niveau VIP
      if (customerData.vipLevel && customerData.vipLevel !== 'Bronze') {
        benefits.push({ icon: '🚀', text: 'Multiplicateur de points' });
      }
      
      if (customerData.vipLevel === 'Or' || customerData.vipLevel === 'Platine') {
        benefits.push({ icon: '🚚', text: 'Livraison gratuite' });
      }
      
      if (customerData.vipLevel === 'Platine') {
        benefits.push({ icon: '👑', text: 'Service client prioritaire' });
      }
      
      // Reconstruire la liste des avantages
      benefitsListElement.innerHTML = benefits.map(benefit => `
        <div class="benefit-item">
          <span class="benefit-icon">${benefit.icon}</span>
          <span class="benefit-text">${benefit.text}</span>
        </div>
      `).join('');
    }
    
    // Initialiser
    setTimeout(updateAccountInfo, 1000);
    
    // Écouter les mises à jour du widget de fidélité
    window.addEventListener('loyaltyPointsUpdated', updateAccountInfo);
  });
</script>

{% schema %}
{
  "name": "Widget Fidélité Compte",
  "target": "section",
  "settings": [
    {
      "type": "header",
      "content": "Configuration du widget"
    },
    {
      "type": "checkbox",
      "id": "show_progress_bar",
      "label": "Afficher la barre de progression",
      "default": true,
      "info": "Montre la progression vers le niveau suivant"
    },
    {
      "type": "checkbox",
      "id": "show_benefits",
      "label": "Afficher les avantages",
      "default": true,
      "info": "Liste les avantages du niveau actuel"
    },
    {
      "type": "checkbox",
      "id": "show_quick_actions",
      "label": "Afficher les actions rapides",
      "default": true,
      "info": "Boutons pour récompenses, activité, parrainage"
    },
    {
      "type": "text",
      "id": "custom_title",
      "label": "Titre personnalisé",
      "placeholder": "Programme BROpoints",
      "info": "Titre affiché en haut du widget"
    }
  ]
}
{% endschema %}
