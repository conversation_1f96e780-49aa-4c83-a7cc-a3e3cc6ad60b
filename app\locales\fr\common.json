{"loyalty": {"points": {"balance": "Points disponibles", "earn": "Gagnez {{points}} points", "spend": "Dépensez vos points"}, "referral": {"invite": "Inviter un ami", "reward": "Gagnez {{points}} points pour chaque parrainage"}, "vip": {"status": "Statut VIP", "progress": "Progression vers le statut VIP", "levels": {"BRO": "BRO", "EhrenBRO": "EhrenBRO", "BesterBRO": "Bester <PERSON>"}, "benefits": {"BRO": "Taux de gain de base", "EhrenBRO": "25% de points bonus sur tous les achats", "BesterBRO": "50% de points bonus sur tous les achats\nSupport client prioritaire"}}}, "common": {"save": "Enregistrer", "cancel": "Annuler", "loading": "Chargement...", "error": "Une erreur est survenue", "search": "Rechercher...", "logout": "Déconnexion", "noResults": "Aucun résultat trouvé", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "add": "Ajouter", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "view": "Voir", "back": "Retour", "next": "Suivant", "previous": "Précédent", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "success": "Su<PERSON>ès", "warning": "Attention", "info": "Information", "name": "Nom", "description": "Description", "status": "Statut", "actions": "Actions", "settings": "Paramètres", "configuration": "Configuration", "all": "Tous"}, "admin": {"navigation": {"dashboard": "Tableau de bord", "program": "Programme", "customers": "Clients", "analytics": "Analyses", "settings": "Paramètres", "promotions": "Promotions", "history": "Historique", "pointsShop": "Boutique de points", "overview": "Vue d'ensemble", "pointsConfig": "Configuration des points", "referrals": "Parrainage", "vipProgram": "Programme VIP", "bonusCampaigns": "<PERSON>ag<PERSON> bonus", "generalSettings": "Paramètres généraux", "customizeWidget": "Personnaliser le widget", "exchangeableProducts": "Produits échangeables"}, "dashboard": {"title": "Tableau de bord", "totalPoints": "Points totaux", "activeMembers": "Membres actifs", "redemptionRate": "Taux d'utilisation", "pointsEvolution": "Évolution des points (30 jours)", "pointsDistribution": "Distribution des points par type", "recentActivity": "Activité récente", "loadingChart": "Chargement du graphique...", "tableHeaders": {"customer": "Client", "action": "Action", "points": "Points", "date": "Date"}, "averagePointsPerCustomer": "Points moyens / client", "dataAvailableSoon": "Les données seront bientôt disponibles...", "pointsInCirculation": "Points en circulation", "rewardsThisMonth": "Récompenses ce mois"}, "program": {"title": "Programme de fidélité", "overview": "Vue d'ensemble", "status": {"active": "Programme actif", "inactive": "Programme inactif", "activate": "Activer", "deactivate": "Désactiver", "activeDescription": "Votre programme de fidélité est actuellement actif et vos clients peuvent gagner des points.", "inactiveDescription": "Votre programme est actuellement inactif. Activez-le pour permettre à vos clients de gagner des points."}, "generalConfiguration": "Configuration générale", "programName": "Nom du programme", "programDescription": "Description du programme", "quickActions": "Actions rapides", "pointsConfiguration": "Configuration des points", "referralProgram": "Programme de parrainage", "stats": {"title": "Statistiques du programme", "totalCustomers": "C<PERSON><PERSON> <PERSON><PERSON>", "activeCustomers": "Clients actifs", "totalPointsEarned": "Points gagnés to<PERSON>ux", "totalPointsRedeemed": "Points utilisés totaux", "totalRewards": "Récompenses totales", "pendingReferrals": "Parrainages en attente", "completedReferrals": "Parrainages complétés", "pointsDistributed": "Points distribués"}, "paramSaveSuccess": "Paramètres sauvegardés avec succès", "paramSaveError": "Erreur lors de la sauvegarde des paramètres", "saveModifications": "Enregistrer les modifications", "saveDescription": "Vous avez des modifications non sauvegardées"}, "customers": {"title": "Clients", "member": "Membre", "guest": "Invi<PERSON>", "points": "points", "referrals": "personne(s)", "email": "Email", "joinedOn": "Inscrit le", "type": "Type", "filters": {"type": "Type", "search": "Rechercher par nom ou email"}, "pagination": "Page {{current}} sur {{total}}", "totalCustomers": "{{total}} client(s) au total", "filteredFrom": "filtré(s) sur {{total}}", "back": "Retour", "viewInShopify": "Voir dans Shopify", "infoTitle": "Informations client", "activityTitle": "Activité", "pointsTab": "Points", "referralsTab": "Parrainages", "rewardsTab": "Récompenses", "ordersTitle": "Commandes", "orderId": "ID Commande", "total": "Total", "status": "Statut", "date": "Date", "noOrders": "Aucune commande", "currentBalance": "Solde actuel", "statsTitle": "Statistiques", "totalSpent": "Total dépensé", "ordersCount": "Commandes", "completedReferrals": "Parrainages", "referralTitle": "Parrainage", "referralFeatureComing": "Fonctionnalité à développer", "referralCodeInfo": "Code de parrainage et lien à afficher ici", "action": "Action", "referee": "<PERSON>lle<PERSON>", "referralStatus": "Statut", "referralOrderTotal": "Commande total", "reward": "Récompense", "code": "Code", "noRewards": "Aucune récompense échangée", "none": "—", "earned": "<PERSON><PERSON><PERSON><PERSON>", "redeemed": "<PERSON><PERSON><PERSON>", "signup": "Inscription", "validated": "<PERSON><PERSON><PERSON>", "pending": "En attente", "paid": "<PERSON><PERSON>", "anonymous": "Client anonyme", "client": "Client", "errors": {"customerIdRequired": "ID client requis", "customerNotFound": "Client non trouvé", "actionNotRecognized": "Action non reconnue"}, "success": {"referralLinkGenerated": "Lien de parrainage généré avec succès"}, "referralInterface": {"currentReferralLink": "Lien de parrainage actuel", "code": "Code", "expiresOn": "Expire le", "copied": "Copié !", "copyLink": "Copier le lien", "noActiveReferralLink": "Aucun lien de parrainage actif", "generateReferralLink": "Générer un lien de parrainage", "referralTableHeaders": {"referee": "<PERSON>lle<PERSON>", "code": "Code", "status": "Statut", "validationDate": "Date validation", "expirationDate": "Date expiration", "pointsEarned": "Points gagnés"}, "referralStatuses": {"pending": "En attente", "completed": "<PERSON><PERSON><PERSON>", "expired": "Expiré"}, "referralStats": {"title": "Statistiques de Parrainage", "referralsSent": "Parrainages envoyés", "referralsValidated": "Parrainages validés", "pending": "En attente", "pointsEarned": "Points gagnés", "currentLink": "Lien actuel", "activeUntil": "Actif jusqu'au", "referredBy": "Parrainé par"}}}, "analytics": {"title": "Analyses", "subtitle": "Analyses du programme de fidélité", "memberStats": "Statistiques des membres", "totalMembers": "<PERSON><PERSON><PERSON>", "newMembersLast30Days": "Nouveaux membres (30 jours)", "pointsTransactions": "Transactions de points", "totalTransactions": "Transactions totales", "pointsDistributed": "Points distribués", "referralPurchases": "Achats par parrainage", "referralRevenue": "Revenus par parrainage", "trends": "Tendances", "membersGrowth": "Croissance des membres", "pointsGrowth": "Croissance des points", "revenueGrowth": "Croissance des revenus"}, "settings": {"title": "Paramètres", "quickNavigation": "Navigation rapide", "customizeWidget": "Personnaliser le widget", "exchangeableProducts": "Produits échangeables", "generalSettings": "Paramètres généraux", "shopName": "Nom de la boutique", "currency": "<PERSON><PERSON>", "language": "<PERSON><PERSON>", "emailNotifications": "Notifications par email", "pointsName": "Nom des points", "welcomeMessage": "Message de bienvenue", "saveSuccess": "Paramètres sauvegardés avec succès", "saveError": "<PERSON><PERSON><PERSON> lors de la sauvegarde", "customizationTitle": "Personnalisation", "pointsNameHelp": "Ce nom sera utilisé partout dans l'application", "welcomeMessageHelp": "Message affiché aux nouveaux clients", "notificationsTitle": "Notifications", "senderEmail": "<PERSON>ail expéditeur", "saveCardTitle": "💾 Sauvegarder les modifications", "saveButton": "Enregistrer les modifications", "noChanges": "Aucune modification", "unsavedChanges": "Vous avez des modifications non sauvegardées", "senderEmailHelp": "Laissez vide pour utiliser la configuration par défaut"}, "emails": {"title": "Configuration des Emails", "subtitle": "<PERSON><PERSON><PERSON> les notifications par email du programme de fidélité", "backToSettings": "Paramètres", "configurationStatus": "État de la Configuration", "emailService": "Service d'emails", "configured": "<PERSON><PERSON>gu<PERSON>", "notConfigured": "Non configuré", "notificationsEnabled": "Notifications activées", "enabled": "Activées", "disabled": "Désactivées", "generalSettings": "Paramètres Généraux", "enableNotifications": "Activer les notifications par email", "enableNotificationsHelp": "Les clients recevront des emails pour les événements du programme de fidélité", "shopName": "Nom de la boutique", "shopNameHelp": "Nom affiché dans les emails", "pointsName": "Nom des points", "pointsNameHelp": "Nom utilisé pour les points dans les emails (ex: BROpoints)", "automaticEmailTypes": "Types d'Emails Automatiques", "welcomeEmail": "Email de bienvenue", "welcomeEmailDesc": "Envoy<PERSON> lo<PERSON> de l'inscription au programme", "pointsEarned": "Points gagnés", "pointsEarnedDesc": "Envoy<PERSON> quand le client gagne des points", "pointsUsed": "Points utilisés", "pointsUsedDesc": "Envoy<PERSON> quand le client utilise des points", "levelPromotion": "Promotion de niveau", "levelPromotionDesc": "Envoy<PERSON> lo<PERSON> d'un changement de niveau VIP", "referralReward": "Récompense de parrainage", "referralRewardDesc": "Envoy<PERSON> lors d'un parrainage réussi", "active": "Actif", "actions": "Actions", "saveSettings": "Sauvegarder les paramètres", "sendTestEmail": "Envoyer un email de test", "testEmailRequirement": "L'email de test nécessite que le service soit configuré et activé", "configuredProvider": "Fournisseur configuré"}, "points": {"title": "Configuration des points", "waysToEarn": "Façons de gagner des points", "waysToRedeem": "Façons d'utiliser les points", "earnDescription": "Configurez les différentes façons dont vos clients peuvent gagner des points. Vous pouvez créer des actions avec des points par euro dépensé (ex: 5 points/€1) ou des points fixes pour des actions spécifiques (ex: 100 points pour l'inscription).", "redeemDescription": "Configurez les récompenses que vos clients peuvent obtenir en échange de leurs points.", "minPoints": "À partir de {{points}} points", "exactPoints": "{{points}} points", "configurable": "Configurable", "fixed": "Fixe", "baseSettings": "Paramètres de base", "earningRateLabel": "Taux de gain (points/€)", "redemptionRateLabel": "Taux de conversion (points/€)", "redemptionRateHelp": "Nombre de points nécessaires pour 1€ de réduction (ex: 100 points = 1€)", "minimumPointsLabel": "Points minimum pour échanger", "expirationDaysLabel": "Expiration des points (jours)", "referralPointsLabel": "Points de parrainage", "birthdayPointsLabel": "Points d'anniversaire", "save": "Enregistrer", "previewTitle": "Prévisualisation", "previewAmountLabel": "Montant d'a<PERSON> (€)", "previewForAmount": "Pour un achat de {amount}€ :", "previewPoints": "Points gagnés : {points} points", "previewValue": "Valeur en € : {value}€", "waysToEarnTitle": "Façons de gagner des points", "addWayToEarn": "Ajouter une façon de gagner", "waysToEarnDescription": "Configurez les différentes façons dont vos clients peuvent gagner des points. Vous pouvez créer des actions avec des points par euro dépensé (ex: 5 points/€1) ou des points fixes pour des actions spécifiques (ex: 100 points pour l'inscription).", "fixedPoints": "{points} points fixes", "pointsPerEuro": "{points} points par €1 dépensé", "active": "Actif", "inactive": "Inactif", "edit": "Modifier", "seeAllWaysToEarn": "Voir toutes les façons de gagner", "seeAllWaysToEarnCount": "Voir toutes les façons de gagner ({count})", "waysToRedeemTitle": "Façons d'échanger des points", "addWayToRedeem": "Ajouter une façon d'échanger", "waysToRedeemDescription": "Configurez les différentes récompenses que vos clients peuvent obtenir en échangeant leurs points. Par exemple, des réductions sur leurs commandes, des produits gratuits, ou la livraison gratuite.", "fromPoints": "À partir de {points} points", "pointsCost": "{points} points", "seeAllWaysToRedeem": "Voir toutes les façons d'échanger", "seeAllWaysToRedeemCount": "Voir toutes les façons d'échanger ({count})", "successUpdate": "Paramètres mis à jour avec succès", "errorUpdate": "Erreur lors de la mise à jour des paramètres", "errorAllFieldsRequired": "Tous les champs sont requis", "errorAllNumbers": "Toutes les valeurs doivent être des nombres valides", "errorPositiveValues": "Les valeurs doivent être positives"}, "widget": {"title": "Personnalisation du widget", "appearance": "Apparence", "colors": "Couleurs", "position": {"label": "Position du widget", "bottomRight": "En bas à droite", "bottomLeft": "En bas à gauche", "topRight": "En haut à droite", "topLeft": "En haut à gauche"}, "size": {"label": "<PERSON><PERSON> du widget", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "large": "Grand"}, "borders": {"label": "Bordures", "square": "Carré", "rounded": "Arrondi", "pill": "<PERSON><PERSON><PERSON>"}, "shadow": "Ombre portée", "animation": "Animation", "showPointsOnButton": "Afficher les points sur le bouton", "primaryColor": "Couleur principale", "secondaryColor": "<PERSON>uleur secondaire", "textColor": "Couleur du texte", "preview": "<PERSON><PERSON><PERSON><PERSON>", "previewDescription": "Aperçu en temps réel de votre widget", "loyaltyProgram": "Programme de Fidélité", "welcomeTo": "Bienvenue dans", "welcomeMessage": "Bienvenue dans notre programme de fidélité !", "guest": "Invi<PERSON>", "member": "Membre", "points": "Points", "orders": "Commandes", "nextReward": "Prochaine récompense", "pointsNeeded": "{{count}} points restants", "yourRewards": "Vos récompenses", "oneRewardAvailable": "Vous avez 1 récompense disponible", "waysToEarn": "Façons de gagner", "waysToRedeem": "Façons d'utiliser", "referFriends": "Pa<PERSON>inez vos amis", "referralsCompleted": "{{count}} parrainages complétés", "shareUrl": "Partagez ce lien pour offrir à vos amis un coupon de 4€", "facebook": "Facebook", "x": "X", "email": "Email", "yourActivity": "Votre activité", "poweredBy": "Propulsé par Loyalty App", "brandPresets": {"title": "Presets de marque BROpoints", "description": "Presets de couleurs rapides qui correspondent à l'identité de marque BROpoints", "classic": {"name": "BROpoints Classique", "description": "Couleurs officielles de la marque BROpoints"}, "dark": {"name": "BROpoints Sombre", "description": "Thème sombre avec accents verts"}, "light": {"name": "BROpoints Clair", "description": "Thème clair avec verts subtils"}, "gold": {"name": "BROpoints Or", "description": "Combinaison premium or et vert"}, "blue": {"name": "BROpoints Bleu", "description": "<PERSON><PERSON><PERSON> bleue professionnelle"}, "customPresets": "Presets personnalisés", "saveAsPreset": "Enregistrer comme preset", "presetName": "Nom du preset", "editPreset": "Modifier le preset", "deletePreset": "Supprimer le preset", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce preset ?", "presetSaved": "Preset personnalisé enregistré avec succès", "presetDeleted": "Preset personnalisé supprimé avec succès", "bropointsclassic": {"name": "BROpoints Classique", "description": "Couleurs officielles de la marque BROpoints"}, "bropointsdark": {"name": "BROpoints Sombre", "description": "Thème sombre avec accents verts"}, "bropointslight": {"name": "BROpoints Clair", "description": "Thème clair avec verts subtils"}, "bropointsgold": {"name": "BROpoints Or", "description": "Combinaison premium or et vert"}, "bropointsblue": {"name": "BROpoints Bleu", "description": "<PERSON><PERSON><PERSON> bleue professionnelle"}}, "cssEditor": {"title": "Éditeur CSS avancé", "description": "Ajoutez du CSS personnalisé pour personnaliser davantage l'apparence de votre widget BROpoints. Utilisez les variables CSS spécifiques à BROpoints pour un thème cohérent.", "placeholder": "/* Variables CSS BROpoints */\n:root {\n  --loyalty-primary: {primaryColor};\n  --loyalty-secondary: {secondaryColor};\n  --loyalty-text-color: {textColor};\n}\n\n/* Style de widget personnalisé */\n.loyalty-widget-container {\n  /* Vos styles personnalisés ici */\n}\n\n/* Personnalisation de l'icône BROpoints */\n.bropoints-icon {\n  /* Style de l'icône */\n}", "helpText": "Utilisez les variables CSS comme --loyalty-primary, --loyalty-secondary, et --bropoints-icon pour un thème cohérent", "tips": {"title": "Conseils CSS BROpoints :", "tip1": "Utilisez .loyalty-widget-container pour styliser le widget principal", "tip2": "Utilisez .bropoints-icon pour personnaliser l'icône BROpoints", "tip3": "Utilisez les variables --loyalty-primary et --loyalty-secondary", "tip4": "Ciblez .loyalty-panel pour le style de popup"}}}, "notifications": {"languageChanged": "Langue changée vers {{language}}"}, "referrals": {"title": "Programme de parrainage", "description": "Le programme de parrainage permet à vos clients fidèles de recommander votre boutique à leurs amis et famille. Quand un client parraine un ami qui effectue son premier achat, les deux parties reçoivent des récompenses. C'est un excellent moyen d'acquérir de nouveaux clients tout en récompensant la fidélité de vos clients existants.", "howItWorks": "Comment ça fonctionne :", "step1": "Le parrain partage son code de parrainage unique avec ses amis", "step2": "Le filleul utilise ce code lors de sa première commande", "step3": "Si la commande atteint le montant minimum, les récompenses sont distribuées", "step4": "Le parrain et le filleul reçoivent leurs récompenses respectives", "programStatus": "État du programme", "active": "Actif", "inactive": "Inactif", "activate": "Activer", "deactivate": "Désactiver", "activeDescription": "Vos clients peuvent actuellement parrainer leurs amis et gagner des récompenses.", "inactiveDescription": "Le programme de parrainage est actuellement désactivé. Activez-le pour permettre à vos clients de parrainer leurs amis.", "referrerRewardTitle": "Récompense du parrain", "referrerGets": "Le parrain reçoit {reward}", "referredRewardTitle": "Récompense du filleul", "referredGets": "<PERSON> fill<PERSON>l reçoit {reward}", "rewardTypeLabel": "Type de récompense", "rewardTypePoints": "Points", "rewardTypeFixed": "Réduction fixe (€)", "rewardTypeDiscount": "Pourcentage (%)", "rewardAmountPoints": "<PERSON><PERSON> en points", "rewardAmountFixed": "Montant de la réduction (€)", "rewardAmountDiscount": "Pourcentage de réduction (%)", "rewardAmountDefault": "<PERSON><PERSON> récompense", "conditionsTitle": "Conditions", "minPurchaseLabel": "Montant minimum d'achat pour le filleul (€)", "minPurchaseHelp": "Montant minimum que le filleul doit dépenser pour valider le parrainage", "expiryDaysLabel": "Durée de validité de l'invitation (jours)", "expiryDaysHelp": "Nombre de jours pendant lesquels l'invitation reste valide", "customizationTitle": "Personnalisation", "customMessageLabel": "Message d'invitation", "customMessageHelp": "Ce message sera affiché sur la page de parrainage", "save": "Enregistrer les modifications", "referralLinksTitle": "Gestion des liens de parrainage", "referralLinksDescription": "<PERSON><PERSON><PERSON><PERSON> et gérez les liens de parrainage pour vos clients.", "customerTableName": "Nom", "customerTableEmail": "Email", "customerTableType": "Type", "customerTablePoints": "Points", "customerTableStatus": "Statut Parrainage", "customerTableAction": "Action", "member": "Membre", "guest": "Invi<PERSON>", "linkActive": "Lien actif", "noLink": "Pas de lien", "generateLink": "Générer lien", "existingLink": "Lien existant", "noCustomers": "Aucun client trouvé. Assurez-vous d'avoir des clients dans votre boutique.", "linkFormatTitle": "Format des liens de parrainage", "linkFormatDescription": "Les liens générés suivent ce format :", "linkFormatExample": "https://votre-boutique.myshopify.com?ref=eyxxxxxxxxxxxxxxxx", "linkFormatHelp": "Où \"eyxxxxxxxxxxxxxxxx\" est un token unique sécurisé en base64.", "linkHowItWorks": "Comment ça fonctionne :", "linkStep1": "Le client génère son lien via le widget de fidélité", "linkStep2": "Il partage le lien sur les réseaux sociaux ou par email", "linkStep3": "Un ami clique sur le lien et est redirigé vers votre boutique", "linkStep4": "Le code est capturé automatiquement et stocké", "linkStep5": "<PERSON><PERSON> de l'achat, le parrainage est validé et les récompenses distribuées", "statsTitle": "Statistiques de parrainage", "statsTotal": "Total parrainages", "statsCompleted": "Complétés", "statsPending": "En attente", "statsConversion": "Taux de conversion", "helpTitle": "Aide", "helpDescription1": "Le programme de parrainage permet à vos clients de recommander votre boutique à leurs amis. Les parrains et les filleuls reçoivent des récompenses lorsque le parrainage est validé.", "helpDescription2": "Un parrainage est validé lorsque le filleul effectue son premier achat atteignant le montant minimum défini."}, "vipProgram": {"title": "Programme VIP", "subtitle": "Système de niveaux BROpoints", "description": "Le système de niveaux BROpoints promeut automatiquement les clients en fonction du total de points gagnés. Les trois niveaux (BRO, EhrenBRO, Bester BRO) offrent des multiplicateurs de points croissants pour récompenser les clients fidèles.", "programStatus": "Statut du programme", "active": "Actif", "inactive": "Inactif", "enable": "Activer", "disable": "Désactiver", "enableDescription": "Activer le programme VIP pour permettre aux clients de progresser dans les niveaux", "disableDescription": "Désactiver le programme VIP pour arrêter la progression des niveaux", "levels": {"title": "Niveaux VIP", "configured": "{count} niveaux configurés", "addLevel": "Ajouter un niveau", "editLevel": "Modifier le niveau", "deleteLevel": "Supp<PERSON><PERSON> le niveau", "name": "Nom du niveau", "threshold": "<PERSON><PERSON> (points)", "multiplier": "Multiplicateur de points", "benefits": "Avantages", "actions": "Actions", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer le niveau", "cancel": "Annuler", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce niveau ?", "deleteWarning": "Cette action ne peut pas être annulée. Les clients à ce niveau seront déplacés vers le niveau disponible le plus proche.", "thresholdHelp": "Points minimum requis pour atteindre ce niveau", "multiplierHelp": "Multiplicateur de points pour les clients à ce niveau (ex: 1,25 = 25% de bonus)", "benefitsHelp": "Description des avantages pour ce niveau (un par ligne)", "nameRequired": "Le nom du niveau est requis", "thresholdRequired": "Le seuil est requis", "multiplierRequired": "Le multiplicateur est requis", "thresholdMustBeNumber": "Le seuil doit être un nombre valide", "multiplierMustBeNumber": "Le multiplicateur doit être un nombre valide", "thresholdMustBePositive": "Le seuil doit être positif", "multiplierMustBePositive": "Le multiplicateur doit être positif"}, "evaluation": {"title": "Évaluation des niveaux", "description": "Les niveaux des clients sont évalués automatiquement lorsque les points sont gagnés ou échangés. Les niveaux sont basés sur le total de points accumulés, pas sur les montants dépensés.", "retentionPeriod": "Période de rétention du statut (jours)", "retentionPeriodHelp": "<PERSON><PERSON><PERSON> pendant laquelle un client conserve son statut de niveau", "evaluationPeriod": "Période d'évaluation (jours)", "evaluationPeriodHelp": "Fréquence de réévaluation du statut de niveau", "saveSettings": "Enregistrer les paramètres"}, "statistics": {"title": "Statistiques des niveaux", "customers": "Clients {level} : {count}", "totalCustomers": "Total clients : {count}"}, "help": {"title": "Aide", "description": "Le système de niveaux BROpoints récompense vos meilleurs clients avec des avantages exclusifs. Les clients sont automatiquement promus en fonction du total de points gagnés.", "progression": "La progression de niveau est immédiate lorsque les clients atteignent les seuils de points requis. Les niveaux supérieurs offrent de meilleurs multiplicateurs de points pour toutes les activités de gain.", "levelInfo": "BRO (0 points) : Niveau de base avec multiplicateur 1x\nEhrenBRO (1000 points) : Multiplicateur de points 1,25x\nBester BRO (5000 points) : Multiplicateur de points 1,5x"}, "messages": {"levelSaved": "Niveau enregistré avec succès", "levelDeleted": "Niveau supprimé avec succès", "settingsSaved": "Paramètres enregistrés avec succès", "errorSaving": "Erreur lors de l'enregistrement des modifications", "errorDeleting": "<PERSON><PERSON><PERSON> lors de la suppression du niveau", "errorLoading": "Erreur lors du chargement des données du programme VIP"}}, "exchangeableProducts": {"title": "Produits échangeables", "subtitle": "<PERSON><PERSON><PERSON> les produits que vos clients peuvent obtenir en échange de points", "addProduct": "Ajouter un produit", "emptyStateHeading": "Aucun produit échangeable configuré", "emptyStateDescription": "Commencez par ajouter des produits que vos clients peuvent échanger contre des points.", "product": "Produit", "image": "Image", "pointsCost": "Coût en points", "status": "Statut", "actions": "Actions", "calculatedAuto": "Calculé automatiquement", "active": "Actif", "inactive": "Inactif", "activate": "Activer", "deactivate": "Désactiver", "delete": "<PERSON><PERSON><PERSON><PERSON>", "successAdd": "Ajout de {count} produit(s) en cours...", "successDelete": "Produit échangeable supprimé avec succès", "successToggle": "Statut du produit {status} avec succès", "errorSelectOne": "Veuillez sélectionner au moins un produit", "modalTitle": "Ajouter un produit échangeable", "modalPrimary": "Ajouter", "modalSecondary": "Annuler", "modalDescription": "Sélectionnez les produits que vos clients pourront échanger contre des points. Le coût en points sera défini dans les paramètres du programme.", "selectedCount": "{count} produit(s) sélectionné(s). Le coût en points sera configuré automatiquement selon les paramètres du programme."}, "productSelector": {"title": "Produits échangeables avec des points", "addProducts": "Ajouter des produits", "description": "Sélectionnez les produits que vos clients peuvent acheter avec leurs points de fidélité.", "empty": "Aucun produit sélectionné. Cliquez sur 'Ajouter des produits' pour commencer.", "price": "Prix", "remove": "<PERSON><PERSON><PERSON><PERSON>", "selectProducts": "Sélectionner des produits", "close": "<PERSON><PERSON><PERSON>", "searchLabel": "Rechercher des produits", "searchPlaceholder": "Nom du produit...", "loading": "Chargement des produits...", "selected": "Sélectionné"}}}