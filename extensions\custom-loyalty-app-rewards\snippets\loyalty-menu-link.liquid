{% comment %}
  Snippet : Lien de menu pour le programme de fidélité
  Usage : {% render 'loyalty-menu-link' %}
  
  Paramètres optionnels :
  - text: Texte du lien (défaut: "Club BROpoints")
  - show_points: Afficher le solde de points (défaut: true)
  - class: Classes CSS supplémentaires
{% endcomment %}

{% assign link_text = text | default: "Club BROpoints" %}
{% assign show_points = show_points | default: true %}
{% assign css_class = class | default: "loyalty-menu-link" %}

<a href="#" 
   onclick="window.loyaltyWidget.open(); return false;" 
   class="{{ css_class }}"
   aria-label="Ouvrir le programme de fidélité">
  
  <span class="loyalty-menu-text">{{ link_text }}</span>
  
  {% if customer and show_points %}
    <span class="loyalty-points-badge" id="header-points-display" data-loyalty-points>
      <span class="loyalty-loading">...</span>
    </span>
  {% endif %}
</a>

<style>
  .loyalty-menu-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
  }
  
  .loyalty-menu-link:hover {
    opacity: 0.8;
  }
  
  .loyalty-points-badge {
    background: #2E7D32;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
  }
  
  .loyalty-loading {
    opacity: 0.7;
  }
  
  @media (max-width: 768px) {
    .loyalty-menu-link {
      flex-direction: column;
      gap: 4px;
    }
    
    .loyalty-points-badge {
      font-size: 10px;
      padding: 1px 6px;
    }
  }
</style>

<script>
  // Mettre à jour l'affichage des points dans le menu
  function updateMenuPoints() {
    const badge = document.getElementById('header-points-display');
    if (badge && window.loyaltyWidget && window.loyaltyWidget.customerData) {
      const points = window.loyaltyWidget.customerData.points || 0;
      badge.innerHTML = `${points} pts`;
    }
  }
  
  // Initialiser l'affichage des points
  document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateMenuPoints, 2000);
  });
  
  // Écouter les mises à jour de points
  window.addEventListener('loyaltyPointsUpdated', updateMenuPoints);
</script>
