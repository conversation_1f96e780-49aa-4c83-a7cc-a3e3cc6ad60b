#!/usr/bin/env node

/**
 * Script de test pour le système d'emails
 * Usage: node scripts/test-email-system.mjs
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Charger les variables d'environnement
dotenv.config();

const prisma = new PrismaClient();

// Configuration de test
const TEST_CONFIG = {
  shop: 'test-shop.myshopify.com',
  testEmail: process.env.TEST_EMAIL || '<EMAIL>',
  testCustomerName: 'Test Customer'
};

/**
 * Simuler l'envoi d'un email (version simplifiée pour les tests)
 */
async function simulateEmailSend(emailType, emailData) {
  console.log(`\n📧 Simulation d'envoi d'email:`);
  console.log(`   Type: ${emailType}`);
  console.log(`   Destinataire: ${emailData.to}`);
  console.log(`   Nom: ${emailData.toName || 'Non spécifié'}`);
  console.log(`   Données: ${JSON.stringify(emailData.templateData, null, 2)}`);
  
  // Simuler un délai d'envoi
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return { success: true, messageId: `test_${Date.now()}` };
}

/**
 * Créer un client de test
 */
async function createTestCustomer() {
  console.log('🔧 Création du client de test...');
  
  try {
    // Supprimer le client de test s'il existe déjà
    await prisma.customer.deleteMany({
      where: {
        email: TEST_CONFIG.testEmail,
        shop: TEST_CONFIG.shop
      }
    });

    // Créer le client de test
    const customer = await prisma.customer.create({
      data: {
        customerId: 'test_customer_123',
        shop: TEST_CONFIG.shop,
        firstName: 'Test',
        lastName: 'Customer',
        email: TEST_CONFIG.testEmail,
        type: 'member',
        points: 500,
        vipLevel: 'BRO',
        totalSpent: 150.00,
        ordersCount: 3
      }
    });

    console.log(`✅ Client de test créé: ${customer.email} (ID: ${customer.id})`);
    return customer;
  } catch (error) {
    console.error('❌ Erreur lors de la création du client de test:', error);
    throw error;
  }
}

/**
 * Créer les paramètres de test
 */
async function createTestSettings() {
  console.log('🔧 Configuration des paramètres de test...');
  
  try {
    await prisma.settings.upsert({
      where: { shop: TEST_CONFIG.shop },
      create: {
        shop: TEST_CONFIG.shop,
        shopName: 'Boutique de Test',
        pointsName: 'BROpoints',
        emailNotifications: true,
        currency: 'EUR'
      },
      update: {
        emailNotifications: true,
        shopName: 'Boutique de Test',
        pointsName: 'BROpoints'
      }
    });

    console.log('✅ Paramètres de test configurés');
  } catch (error) {
    console.error('❌ Erreur lors de la configuration des paramètres:', error);
    throw error;
  }
}

/**
 * Tester l'email de bienvenue
 */
async function testWelcomeEmail(customer) {
  console.log('\n🧪 Test de l\'email de bienvenue...');
  
  const emailData = {
    to: customer.email,
    toName: `${customer.firstName} ${customer.lastName}`,
    templateData: {
      customerName: `${customer.firstName} ${customer.lastName}`,
      welcomePoints: 100,
      shopName: 'Boutique de Test',
      shopUrl: `https://${TEST_CONFIG.shop}`,
      pointsName: 'BROpoints'
    }
  };

  const result = await simulateEmailSend('WELCOME', emailData);
  
  if (result.success) {
    console.log('✅ Email de bienvenue simulé avec succès');
  } else {
    console.log('❌ Échec de la simulation de l\'email de bienvenue');
  }
  
  return result;
}

/**
 * Tester l'email de points gagnés
 */
async function testPointsEarnedEmail(customer) {
  console.log('\n🧪 Test de l\'email de points gagnés...');
  
  const emailData = {
    to: customer.email,
    toName: `${customer.firstName} ${customer.lastName}`,
    templateData: {
      customerName: `${customer.firstName} ${customer.lastName}`,
      points: 50,
      totalPoints: customer.points + 50,
      reason: 'Commande #TEST123',
      shopName: 'Boutique de Test',
      pointsName: 'BROpoints'
    }
  };

  const result = await simulateEmailSend('POINTS_EARNED', emailData);
  
  if (result.success) {
    console.log('✅ Email de points gagnés simulé avec succès');
  } else {
    console.log('❌ Échec de la simulation de l\'email de points gagnés');
  }
  
  return result;
}

/**
 * Tester l'email de points utilisés
 */
async function testPointsRedeemedEmail(customer) {
  console.log('\n🧪 Test de l\'email de points utilisés...');
  
  const emailData = {
    to: customer.email,
    toName: `${customer.firstName} ${customer.lastName}`,
    templateData: {
      customerName: `${customer.firstName} ${customer.lastName}`,
      points: 200,
      remainingPoints: customer.points - 200,
      rewardDescription: 'Coupon de 20€',
      shopName: 'Boutique de Test',
      pointsName: 'BROpoints'
    }
  };

  const result = await simulateEmailSend('POINTS_REDEEMED', emailData);
  
  if (result.success) {
    console.log('✅ Email de points utilisés simulé avec succès');
  } else {
    console.log('❌ Échec de la simulation de l\'email de points utilisés');
  }
  
  return result;
}

/**
 * Tester l'email de promotion de niveau
 */
async function testLevelUpEmail(customer) {
  console.log('\n🧪 Test de l\'email de promotion de niveau...');
  
  const emailData = {
    to: customer.email,
    toName: `${customer.firstName} ${customer.lastName}`,
    templateData: {
      customerName: `${customer.firstName} ${customer.lastName}`,
      newLevel: 'EhrenBRO',
      previousLevel: 'BRO',
      bonusPoints: 100,
      shopName: 'Boutique de Test',
      pointsName: 'BROpoints'
    }
  };

  const result = await simulateEmailSend('LEVEL_UP', emailData);
  
  if (result.success) {
    console.log('✅ Email de promotion de niveau simulé avec succès');
  } else {
    console.log('❌ Échec de la simulation de l\'email de promotion de niveau');
  }
  
  return result;
}

/**
 * Tester l'email de récompense de parrainage
 */
async function testReferralRewardEmail(customer) {
  console.log('\n🧪 Test de l\'email de récompense de parrainage...');
  
  const emailData = {
    to: customer.email,
    toName: `${customer.firstName} ${customer.lastName}`,
    templateData: {
      customerName: `${customer.firstName} ${customer.lastName}`,
      points: 150,
      referredCustomerName: 'Ami Parrainé',
      shopName: 'Boutique de Test',
      pointsName: 'BROpoints'
    }
  };

  const result = await simulateEmailSend('REFERRAL_REWARD', emailData);
  
  if (result.success) {
    console.log('✅ Email de récompense de parrainage simulé avec succès');
  } else {
    console.log('❌ Échec de la simulation de l\'email de récompense de parrainage');
  }
  
  return result;
}

/**
 * Nettoyer les données de test
 */
async function cleanup() {
  console.log('\n🧹 Nettoyage des données de test...');
  
  try {
    // Supprimer le client de test
    await prisma.customer.deleteMany({
      where: {
        email: TEST_CONFIG.testEmail,
        shop: TEST_CONFIG.shop
      }
    });

    // Supprimer les paramètres de test
    await prisma.settings.deleteMany({
      where: {
        shop: TEST_CONFIG.shop
      }
    });

    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error);
  }
}

/**
 * Vérifier la configuration des emails
 */
function checkEmailConfiguration() {
  console.log('🔍 Vérification de la configuration des emails...');

  const provider = process.env.EMAIL_PROVIDER || 'smtp';
  const emailEnabled = process.env.EMAIL_ENABLED === 'true';

  console.log(`📧 Fournisseur: ${provider}`);
  console.log(`📧 Service d'emails: ${emailEnabled ? 'Activé' : 'Désactivé'}`);
  console.log(`📤 Adresse d'expédition: ${process.env.EMAIL_FROM || 'Non définie'}`);
  console.log(`👤 Nom d'expédition: ${process.env.EMAIL_FROM_NAME || 'Non défini'}`);

  // Vérifier selon le fournisseur
  let requiredVars = ['EMAIL_FROM'];
  let missingVars = [];

  switch (provider) {
    case 'gmail':
    case 'outlook':
      requiredVars = [...requiredVars, 'EMAIL_CLIENT_ID', 'EMAIL_CLIENT_SECRET', 'EMAIL_REFRESH_TOKEN'];
      break;
    case 'smtp':
      requiredVars = [...requiredVars, 'SMTP_HOST', 'SMTP_USER', 'SMTP_PASSWORD'];
      break;
    case 'sendgrid':
      requiredVars = [...requiredVars, 'SENDGRID_API_KEY'];
      break;
  }

  missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.log('⚠️  Variables d\'environnement manquantes pour le fournisseur', provider, ':');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('\n💡 Consultez le fichier .env.email.example pour la configuration');
    return false;
  }

  // Afficher la configuration spécifique
  switch (provider) {
    case 'smtp':
      console.log(`🌐 Serveur SMTP: ${process.env.SMTP_HOST}:${process.env.SMTP_PORT || 587}`);
      console.log(`🔐 Utilisateur SMTP: ${process.env.SMTP_USER}`);
      break;
    case 'gmail':
    case 'outlook':
      console.log(`🔑 Client ID: ${process.env.EMAIL_CLIENT_ID?.substring(0, 20)}...`);
      break;
    case 'sendgrid':
      console.log(`🔑 API Key: ${process.env.SENDGRID_API_KEY?.substring(0, 20)}...`);
      break;
  }

  return true;
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Démarrage du test du système d\'emails\n');
  
  try {
    // Vérifier la configuration
    const configOk = checkEmailConfiguration();
    if (!configOk) {
      console.log('\n❌ Configuration incomplète - arrêt du test');
      return;
    }
    
    // Créer les données de test
    await createTestSettings();
    const customer = await createTestCustomer();
    
    // Exécuter tous les tests
    const results = await Promise.all([
      testWelcomeEmail(customer),
      testPointsEarnedEmail(customer),
      testPointsRedeemedEmail(customer),
      testLevelUpEmail(customer),
      testReferralRewardEmail(customer)
    ]);
    
    // Résumé des résultats
    console.log('\n📊 Résumé des tests:');
    const successCount = results.filter(r => r.success).length;
    console.log(`   ✅ Réussis: ${successCount}/${results.length}`);
    console.log(`   ❌ Échoués: ${results.length - successCount}/${results.length}`);
    
    if (successCount === results.length) {
      console.log('\n🎉 Tous les tests ont réussi ! Le système d\'emails est prêt.');
    } else {
      console.log('\n⚠️  Certains tests ont échoué. Vérifiez la configuration.');
    }
    
  } catch (error) {
    console.error('\n💥 Erreur lors du test:', error);
  } finally {
    // Nettoyer les données de test
    await cleanup();
    await prisma.$disconnect();
  }
}

// Exécuter le script
main().catch(console.error);
